package com.dell.it.hip.controller;


import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.util.ThrottleSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


import java.util.List;


/**
 * Central controller for HIP Integration platform admin APIs.
 */
@RestController
@RequestMapping("/hip/management")
public class HIPIntegrationManagementController {

    @Autowired
    private HIPIntegrationOrchestrationService orchestrationService;

    @Autowired
    private ServiceManager serviceManager;

    // === HIP Integration lifecycle ===

    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestBody HIPIntegrationRequest request) {
        orchestrationService.registerHIPIntegration(request);
        return ResponseEntity.ok("HIPIntegration registered");
    }

    @DeleteMapping("/{name}/{version}")
    public ResponseEntity<?> unregister(@PathVariable String name, @PathVariable String version) {
        orchestrationService.unregisterHIPIntegration(name, version);
        return ResponseEntity.ok("Unregistered");
    }

    @PostMapping("/{name}/{version}/pause")
    public ResponseEntity<?> pause(@PathVariable String name, @PathVariable String version) {
        orchestrationService.pauseHIPIntegration(name, version);
        return ResponseEntity.ok("Paused");
    }

    @PostMapping("/{name}/{version}/resume")
    public ResponseEntity<?> resume(@PathVariable String name, @PathVariable String version) {
        orchestrationService.resumeHIPIntegration(name, version);
        return ResponseEntity.ok("Resumed");
    }

    // === Throttling ===

    @PostMapping("/{name}/{version}/throttle")
    public ResponseEntity<?> throttle(@PathVariable String name, @PathVariable String version, @RequestBody ThrottleSettings settings) {
        orchestrationService.applyThrottle(name, version, settings);
        return ResponseEntity.ok("Throttle applied");
    }

    @DeleteMapping("/{name}/{version}/throttle")
    public ResponseEntity<?> removeThrottle(@PathVariable String name, @PathVariable String version) {
        orchestrationService.removeThrottle(name, version);
        return ResponseEntity.ok("Throttle removed");
    }

    // ==== Per-adapter/handler controls ====

    @PostMapping("/{name}/{version}/adapter/{adapterRef}/pause")
    public ResponseEntity<?> pauseAdapter(@PathVariable String name, @PathVariable String version, @PathVariable String adapterRef) {
        orchestrationService.pauseAdapter(name, version, adapterRef);
        return ResponseEntity.ok("Adapter paused");
    }

    @PostMapping("/{name}/{version}/adapter/{adapterRef}/resume")
    public ResponseEntity<?> resumeAdapter(@PathVariable String name, @PathVariable String version, @PathVariable String adapterRef) {
        orchestrationService.resumeAdapter(name, version, adapterRef);
        return ResponseEntity.ok("Adapter resumed");
    }

    @PostMapping("/{name}/{version}/handler/{handlerRef}/pause")
    public ResponseEntity<?> pauseHandler(@PathVariable String name, @PathVariable String version, @PathVariable String handlerRef) {
        orchestrationService.pauseHandler(name, version, handlerRef);
        return ResponseEntity.ok("Handler paused");
    }

    @PostMapping("/{name}/{version}/handler/{handlerRef}/resume")
    public ResponseEntity<?> resumeHandler(@PathVariable String name, @PathVariable String version, @PathVariable String handlerRef) {
        orchestrationService.resumeHandler(name, version, handlerRef);
        return ResponseEntity.ok("Handler resumed");
    }

    // === SFTP force-poll callback ===

    @PostMapping("/sftp/{integrationName}/{version}/{adapterId}/fileReady")
    public ResponseEntity<?> sftpFileReadyCallback(
            @PathVariable String integrationName,
            @PathVariable String version,
            @PathVariable String adapterId) {
        orchestrationService.triggerSftpForcePoll(integrationName, version, adapterId);
        return ResponseEntity.ok("Force poll triggered for SFTP adapter.");
    }

    // === HIP Inventory/status APIs ===

    @GetMapping("/all")
    public List<HIPIntegrationOrchestrationService.HIPIntegrationInfo> getAll() {
        return orchestrationService.getAllHIPIntegrationsWithStatus();
    }

    @GetMapping("/{name}/{version}/status")
    public IntegrationStatus status(@PathVariable String name, @PathVariable String version) {
        return orchestrationService.getAllHIPIntegrationsWithStatus().stream()
                .filter(i -> i.getHipIntegrationName().equals(name) && i.getVersion().equals(version))
                .map(HIPIntegrationOrchestrationService.HIPIntegrationInfo::getStatus)
                .findFirst()
                .orElse(IntegrationStatus.UNREGISTERED);
    }

    // === (Optional) Diagnostics/Config APIs ===

    @GetMapping("/{name}/{version}/definition")
    public ResponseEntity<?> getDefinition(@PathVariable String name, @PathVariable String version) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(name, version);
        if (def == null) return ResponseEntity.notFound().build();
        return ResponseEntity.ok(def);
    }


}