package com.dell.it.hip.core.registry;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
public interface HIPIntegrationRegistry extends JpaRepository<HIPIntegrationRequestEntity, Long> {
    /**
     * Find all registered integrations for a given service manager (node, environment, or tenant).
     */
    List<HIPIntegrationRequestEntity> findByServiceManagerName(String serviceManagerName);

    /**
     * Find a single integration by manager, name, and version.
     */
    HIPIntegrationRequestEntity findByServiceManagerNameAndHipIntegrationNameAndVersion(
            String serviceManagerName, String hipIntegrationName, String version
    );

    /**
     * Delete a flow by manager, name, and version.
     */
    void deleteByServiceManagerNameAndHipIntegrationNameAndVersion(
            String serviceManagerName, String hipIntegrationName, String version
    );

}