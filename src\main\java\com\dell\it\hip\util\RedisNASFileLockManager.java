package com.dell.it.hip.util;

import com.dell.it.hip.util.NASFileLockManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
@Component
public class RedisNASFileLockManager implements NASFileLockManager {
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Value("${service.manager.name}")
    private String serviceManagerName;

    private String lockKey(String integrationName, String version, String adapterId, String fileName) {
        return String.format("hip:nas:lock:%s:%s:%s:%s:%s",
                serviceManagerName, integrationName, version, adapterId, fileName);
    }

    @Override
    public boolean acquireLock(String integrationName, String version, String adapterId, String fileName, String nodeId, int expireSeconds) {
        String key = lockKey(integrationName, version, adapterId, fileName);
        Boolean success = redisTemplate.opsForValue().setIfAbsent(key, nodeId, expireSeconds, TimeUnit.SECONDS);
        return Boolean.TRUE.equals(success);
    }

    @Override
    public void releaseLock(String integrationName, String version, String adapterId, String fileName) {
        String key = lockKey(integrationName, version, adapterId, fileName);
        redisTemplate.delete(key);
    }
}