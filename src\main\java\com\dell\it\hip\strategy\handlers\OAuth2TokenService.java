package com.dell.it.hip.strategy.handlers;

import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class OAuth2TokenService {
    private static class TokenInfo {
        String accessToken;
        Instant expiry;
    }

    private final Map<String, TokenInfo> tokenCache = new ConcurrentHashMap<>();

    public synchronized String getAccessToken(String tokenUrl, String clientId, String clientSecret,
                                              String scope, String audience, Map<String, String> additionalParams) {
        String cacheKey = tokenUrl + "|" + clientId + "|" + clientSecret + "|" + scope + "|" + audience;
        TokenInfo cached = tokenCache.get(cacheKey);

        if (cached != null && cached.expiry.isAfter(Instant.now().plusSeconds(60))) {
            return cached.accessToken;
        }

        WebClient webClient = WebClient.builder().build();

        MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
        form.add("grant_type", "client_credentials");
        form.add("client_id", clientId);
        form.add("client_secret", clientSecret);
        if (scope != null) form.add("scope", scope);
        if (audience != null) form.add("audience", audience);
        if (additionalParams != null) additionalParams.forEach(form::add);

        Map<String, Object> response = webClient.post()
                .uri(tokenUrl)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .bodyValue(form)
                .retrieve()
                .bodyToMono(Map.class)
                .block();

        String accessToken = response.get("access_token").toString();
        int expiresIn = Integer.parseInt(response.get("expires_in").toString());
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.accessToken = accessToken;
        tokenInfo.expiry = Instant.now().plusSeconds(expiresIn);

        tokenCache.put(cacheKey, tokenInfo);

        return accessToken;
    }
}