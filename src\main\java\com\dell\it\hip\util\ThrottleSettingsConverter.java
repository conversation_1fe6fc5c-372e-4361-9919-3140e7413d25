package com.dell.it.hip.util;

import com.dell.it.hip.util.ThrottleSettings;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class ThrottleSettingsConverter implements AttributeConverter<ThrottleSettings, String> {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public String convertToDatabaseColumn(ThrottleSettings throttleSettings) {
        try {
            return (throttleSettings == null) ? null : objectMapper.writeValueAsString(throttleSettings);
        } catch (Exception e) {
            throw new IllegalArgumentException("Error serializing ThrottleSettings", e);
        }
    }

    @Override
    public ThrottleSettings convertToEntityAttribute(String s) {
        try {
            return (s == null) ? null : objectMapper.readValue(s, ThrottleSettings.class);
        } catch (Exception e) {
            throw new IllegalArgumentException("Error deserializing ThrottleSettings", e);
        }
    }
}