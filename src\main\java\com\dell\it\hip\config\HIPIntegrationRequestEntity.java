package com.dell.it.hip.config;


import jakarta.persistence.*;

import java.time.LocalDateTime;
@Entity
@Table(name = "hip_integration_requests")
public class HIPIntegrationRequestEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String serviceManagerName;

    @Column(nullable = false)
    private String hipIntegrationName;

    @Column(nullable = false)
    private String version;

    @Column(nullable = false)
    private String businessFlowName;

    @Column(length = 1000)
    private String tags;

    @Column
    private String businessFlowType;

    @Column
    private String hipIntegrationType;

    @Column
    private String businessFlowVersion;

    // Store adapters, handlers, steps as J<PERSON><PERSON> blobs
    @Lob
    @Column(name = "adapters_json", columnDefinition = "CLOB")
    private String adaptersJson;

    @Lob
    @Column(name = "handlers_json", columnDefinition = "CLOB")
    private String handlersJson;

    @Lob
    @Column(name = "flow_steps_json", columnDefinition = "CLOB")
    private String flowStepsJson;

    @Lob
    @Column(name = "property_sheets_json", columnDefinition = "CLOB")
    private String propertySheetsJson;

    @Lob
    @Column(name = "throttle_settings_json", columnDefinition = "CLOB")
    private String throttleSettingsJson;

    // Status field, useful for runtime tracking
    @Column(name = "status", length = 50)
    private String status;

    // Timestamps
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Optional: for soft deletes
    @Column(name = "deleted")
    private Boolean deleted = false;

    // --- Getters and Setters ---

    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = this.createdAt;
    }



    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // ... standard getters and setters for all fields ...

    // Example getter/setter for adaptersJson
    public String getAdaptersJson() {
        return adaptersJson;
    }

    public void setAdaptersJson(String adaptersJson) {
        this.adaptersJson = adaptersJson;
    }

    // (Repeat for other fields...)

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getServiceManagerName() {
        return serviceManagerName;
    }

    public void setServiceManagerName(String serviceManagerName) {
        this.serviceManagerName = serviceManagerName;
    }

    public String getHipIntegrationName() {
        return hipIntegrationName;
    }

    public void setHipIntegrationName(String hipIntegrationName) {
        this.hipIntegrationName = hipIntegrationName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getBusinessFlowName() {
        return businessFlowName;
    }

    public void setBusinessFlowName(String businessFlowName) {
        this.businessFlowName = businessFlowName;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getHandlersJson() {
        return handlersJson;
    }

    public void setHandlersJson(String handlersJson) {
        this.handlersJson = handlersJson;
    }

    public String getFlowStepsJson() {
        return flowStepsJson;
    }

    public void setFlowStepsJson(String flowStepsJson) {
        this.flowStepsJson = flowStepsJson;
    }

    public String getPropertySheetsJson() {
        return propertySheetsJson;
    }

    public void setPropertySheetsJson(String propertySheetsJson) {
        this.propertySheetsJson = propertySheetsJson;
    }

    public String getThrottleSettingsJson() {
        return throttleSettingsJson;
    }

    public void setThrottleSettingsJson(String throttleSettingsJson) {
        this.throttleSettingsJson = throttleSettingsJson;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getBusinessFlowType() {
        return businessFlowType;
    }

    public void setBusinessFlowType(String businessFlowType) {
        this.businessFlowType = businessFlowType;
    }

    public String getHipIntegrationType() {
        return hipIntegrationType;
    }

    public void setHipIntegrationType(String hipIntegrationType) {
        this.hipIntegrationType = hipIntegrationType;
    }

    public String getBusinessFlowVersion() {
        return businessFlowVersion;
    }

    public void setBusinessFlowVersion(String businessFlowVersion) {
        this.businessFlowVersion = businessFlowVersion;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}