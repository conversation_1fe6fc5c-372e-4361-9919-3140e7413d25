package com.dell.it.hip.util.adapters;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import jakarta.jms.BytesMessage;
import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.TextMessage;


public class IbmmqHeaderMapperUtil {
    /**
     * Extracts all useful headers from an IBM MQMessage for use as Spring Integration message headers.
     */
    public static Map<String, Object> fromJmsMessage(Message message) throws JMSException {
        Map<String, Object> headers = new HashMap<>();

        // Standard JMS headers
        headers.put("jms_messageId", message.getJMSMessageID());
        headers.put("jms_correlationId", message.getJMSCorrelationID());
        headers.put("jms_destination", message.getJMSDestination() != null ? message.getJMSDestination().toString() : null);
        headers.put("jms_replyTo", message.getJMSReplyTo() != null ? message.getJMSReplyTo().toString() : null);
        headers.put("jms_timestamp", message.getJMSTimestamp());
        headers.put("jms_type", message.getJMSType());
        headers.put("jms_priority", message.getJMSPriority());
        headers.put("jms_redelivered", message.getJMSRedelivered());
        headers.put("jms_deliveryMode", message.getJMSDeliveryMode());
        headers.put("jms_expiration", message.getJMSExpiration());

        // IBM MQ specific - All user properties (headers)
        Enumeration<?> propNames = message.getPropertyNames();
        while (propNames.hasMoreElements()) {
            String prop = propNames.nextElement().toString();
            Object value = message.getObjectProperty(prop);
            headers.put("ibmmq_" + prop, value);
        }

        // Add payload type hint for downstream flow steps if desired
        if (message instanceof BytesMessage) {
            headers.put("ibmmq_payloadType", "bytes");
        } else if (message instanceof TextMessage) {
            headers.put("ibmmq_payloadType", "text");
        }

        return headers;
    }
}