package com.dell.it.hip.config.Handlers;

import java.util.Map;

public class DynamicSftpHandlerConfig {
    private String id;                  // Unique config ref for this handler
    private String host;
    private int port = 22;              // Default SFTP port
    private String username;
    private String password;
    private String privateKeyPath;      // Optional: path to private key for key-based auth
    private String remoteDirectory;     // Directory where files will be uploaded
    private Integer timeout;            // Connection/auth timeout in milliseconds
    private Boolean gzipEnabled;
    private Boolean archiveEnabled;
    private Map<String, Object> parameters;

    // --- Getters and Setters ---

    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }

    public String getHost() {
        return host;
    }
    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }
    public void setPort(int port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }
    public void setPassword(String password) {
        this.password = password;
    }

    public String getPrivateKeyPath() {
        return privateKeyPath;
    }
    public void setPrivateKeyPath(String privateKeyPath) {
        this.privateKeyPath = privateKeyPath;
    }

    public String getRemoteDirectory() {
        return remoteDirectory;
    }
    public void setRemoteDirectory(String remoteDirectory) {
        this.remoteDirectory = remoteDirectory;
    }

    public Integer getTimeout() {
        return timeout;
    }
    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Boolean getGzipEnabled() {
        return gzipEnabled;
    }
    public void setGzipEnabled(Boolean gzipEnabled) {
        this.gzipEnabled = gzipEnabled;
    }

    public Boolean getArchiveEnabled() {
        return archiveEnabled;
    }
    public void setArchiveEnabled(Boolean archiveEnabled) {
        this.archiveEnabled = archiveEnabled;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }
}