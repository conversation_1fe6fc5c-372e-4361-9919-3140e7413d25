package com.dell.it.hip.util.dataformatUtils;

import org.xml.sax.InputSource;

import javax.xml.XMLConstants;
import javax.xml.namespace.NamespaceContext;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.SchemaFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import java.io.StringReader;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.util.*;


public class XmlUtil {

    // Parse XML string to DOM, always namespace-aware
    public static Document parseXml(String xml) throws Exception {
        DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
        dbFactory.setNamespaceAware(true);
        DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
        return dBuilder.parse(new InputSource(new StringReader(xml)));
    }

    // 1. Get root element name (with or without namespace)
    public static String getRootElement(String xml) throws Exception {
        Document doc = parseXml(xml);
        if (doc != null && doc.getDocumentElement() != null) {
            return doc.getDocumentElement().getNodeName();
        }
        return null;
    }

    // 2. Validate well-formedness
    public static boolean isWellFormed(String xml) {
        try {
            parseXml(xml);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // 3. Validate against XSD (schema)
    public static boolean validateSchema(String xml, String xsd) {
        try {
            SchemaFactory factory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
            javax.xml.validation.Schema schema = factory.newSchema(new StreamSource(new StringReader(xsd)));
            javax.xml.validation.Validator validator = schema.newValidator();
            validator.validate(new StreamSource(new StringReader(xml)));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // 4. Extract value by XPath (try with and without namespace)
    public static String extractFieldFlexible(String xml, String xpathExpr, Map<String, String> nsMap) throws Exception {
        Document doc = parseXml(xml);
        XPath xpath = XPathFactory.newInstance().newXPath();

        // Try non-namespaced first
        String result = xpath.evaluate(xpathExpr, doc);
        if (result != null && !result.isEmpty()) return result;

        // Try namespaced (if nsMap provided)
        if (nsMap != null && !nsMap.isEmpty()) {
            xpath.setNamespaceContext(new MapNamespaceContext(nsMap));
            result = xpath.evaluate(xpathExpr, doc);
        }
        return (result == null || result.isEmpty()) ? null : result;
    }

    // 5. Extract attribute by XPath (flexible, as above)
    public static String extractAttributeFlexible(String xml, String xpathExpr, Map<String, String> nsMap) throws Exception {
        return extractFieldFlexible(xml, xpathExpr, nsMap);
    }

    // 6. Extract all attributes from a node (by XPath)
    public static Map<String, String> extractAllAttributes(String xml, String xpathExpr, Map<String, String> nsMap) throws Exception {
        Map<String, String> attributes = new HashMap<>();
        Document doc = parseXml(xml);
        XPath xpath = XPathFactory.newInstance().newXPath();
        if (nsMap != null && !nsMap.isEmpty()) {
            xpath.setNamespaceContext(new MapNamespaceContext(nsMap));
        }
        Node node = (Node) xpath.evaluate(xpathExpr, doc, XPathConstants.NODE);
        if (node != null && node.hasAttributes()) {
            for (int i = 0; i < node.getAttributes().getLength(); i++) {
                Node attr = node.getAttributes().item(i);
                attributes.put(attr.getNodeName(), attr.getNodeValue());
            }
        }
        return attributes;
    }

    // 7. Extract all child elements under a node (by XPath)
    public static Map<String, String> extractAllChildren(String xml, String xpathExpr, Map<String, String> nsMap) throws Exception {
        Map<String, String> children = new HashMap<>();
        Document doc = parseXml(xml);
        XPath xpath = XPathFactory.newInstance().newXPath();
        if (nsMap != null && !nsMap.isEmpty()) {
            xpath.setNamespaceContext(new MapNamespaceContext(nsMap));
        }
        Node node = (Node) xpath.evaluate(xpathExpr, doc, XPathConstants.NODE);
        if (node != null && node.hasChildNodes()) {
            NodeList nodeList = node.getChildNodes();
            for (int i = 0; i < nodeList.getLength(); i++) {
                Node child = nodeList.item(i);
                if (child.getNodeType() == Node.ELEMENT_NODE) {
                    children.put(child.getNodeName(), child.getTextContent());
                }
            }
        }
        return children;
    }

    // 8. Pretty-print XML
    public static String prettyPrint(String xml) {
        try {
            javax.xml.transform.Source xmlInput = new javax.xml.transform.stream.StreamSource(new StringReader(xml));
            java.io.StringWriter stringWriter = new java.io.StringWriter();
            javax.xml.transform.stream.StreamResult xmlOutput = new javax.xml.transform.stream.StreamResult(stringWriter);
            javax.xml.transform.Transformer transformer = javax.xml.transform.TransformerFactory.newInstance().newTransformer();
            transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
            transformer.transform(xmlInput, xmlOutput);
            return xmlOutput.getWriter().toString();
        } catch (Exception e) {
            return xml;
        }
    }

    // 9. Get all namespaces in the document (as map prefix -> URI)
    public static Map<String, String> getNamespaces(String xml) throws Exception {
        Document doc = parseXml(xml);
        Map<String, String> nsMap = new HashMap<>();
        collectNamespaces(doc.getDocumentElement(), nsMap);
        return nsMap;
    }
    private static void collectNamespaces(Node node, Map<String, String> nsMap) {
        if (node == null) return;
        if (node.getNodeType() == Node.ELEMENT_NODE) {
            if (node.getPrefix() != null && node.getNamespaceURI() != null) {
                nsMap.put(node.getPrefix(), node.getNamespaceURI());
            }
            if (node.getAttributes() != null) {
                for (int i = 0; i < node.getAttributes().getLength(); i++) {
                    Node attr = node.getAttributes().item(i);
                    if (attr.getNodeName().startsWith("xmlns:")) {
                        String prefix = attr.getNodeName().substring(6);
                        nsMap.put(prefix, attr.getNodeValue());
                    }
                }
            }
            NodeList children = node.getChildNodes();
            for (int i = 0; i < children.getLength(); i++) {
                collectNamespaces(children.item(i), nsMap);
            }
        }
    }

    // --- Helper class for NamespaceContext ---
    public static class MapNamespaceContext implements NamespaceContext {
        private final Map<String, String> nsMap;
        public MapNamespaceContext(Map<String, String> nsMap) { this.nsMap = nsMap; }
        @Override
        public String getNamespaceURI(String prefix) {
            if (prefix == null) throw new NullPointerException("Null prefix");
            else if ("xml".equals(prefix)) return XMLConstants.XML_NS_URI;
            else if ("xmlns".equals(prefix)) return XMLConstants.XMLNS_ATTRIBUTE_NS_URI;
            return nsMap.getOrDefault(prefix, XMLConstants.NULL_NS_URI);
        }
        @Override
        public String getPrefix(String namespaceURI) {
            for (Map.Entry<String, String> entry : nsMap.entrySet()) {
                if (entry.getValue().equals(namespaceURI)) return entry.getKey();
            }
            return null;
        }
        @Override
        public Iterator<String> getPrefixes(String namespaceURI) {
            return nsMap.entrySet().stream()
                    .filter(e -> e.getValue().equals(namespaceURI))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList()).iterator();
        }
    }
}