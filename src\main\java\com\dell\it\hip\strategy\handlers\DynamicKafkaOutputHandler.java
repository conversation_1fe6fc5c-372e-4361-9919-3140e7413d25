package com.dell.it.hip.strategy.handlers;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.DynamicKafkaHandlerConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.strategy.handlers.AbstractOutputHandlerStrategy;
import com.dell.it.hip.util.*;
import com.dell.it.hip.util.logging.WiretapService;
import org.apache.kafka.clients.producer.*;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
@Component("kafka")

public class DynamicKafkaOutputHandler extends AbstractOutputHandlerStrategy {

    private final Map<String, KafkaProducer<byte[], byte[]>> producerMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicBoolean> handlerPauseState = new ConcurrentHashMap<>();
    private final Set<String> headersToFilter;

    public DynamicKafkaOutputHandler(
            OpenTelemetryPropagationUtil otelUtil,
            WiretapService wiretapService,
            ArchiveService archiveService,
            RetryTemplateFactory retryTemplateFactory,
            Set<String> headersToFilter
    ) {
        super(otelUtil, wiretapService, archiveService, retryTemplateFactory);
        this.headersToFilter = headersToFilter;
    }

    @Override
    public String getType() {
        return "kafka";
    }

    @Override
    protected void doHandle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        // If paused, just throw to trigger orchestration fallback (do NOT wiretap here)
        if (isPaused(def, ref)) {
            throw new IllegalStateException("Handler [" + ref.getType() + "] is paused. Message not delivered.");
        }

        DynamicKafkaHandlerConfig config = def.getConfig(ref.getId(), DynamicKafkaHandlerConfig.class);
        if (config == null)
            throw new IllegalArgumentException("DynamicKafkaHandlerConfig not found for ref: " + ref.getId());

        String topic = config.getTopic();
        if (topic == null)
            throw new IllegalArgumentException("Kafka topic not configured");

        boolean useGzip = Boolean.TRUE.equals(config.getGzipEnabled());

        byte[] payload = useGzip ? CompressionUtil.compress(message.getPayload()) : convertToBytes(message.getPayload());
        Map<String, Object> filteredHeaders = HeaderFilterUtil.filterHeaders(message.getHeaders(), headersToFilter);

        KafkaProducer<byte[], byte[]> producer = getOrCreateProducer(config, ref);

        ProducerRecord<byte[], byte[]> record = new ProducerRecord<>(topic, payload);
        filteredHeaders.forEach((k, v) -> {
            if (v instanceof String) {
                record.headers().add(k, ((String) v).getBytes(StandardCharsets.UTF_8));
            } else if (v instanceof byte[]) {
                record.headers().add(k, (byte[]) v);
            }
        });

        producer.send(record, (metadata, exception) -> {
            if (exception != null) {
                // This is just an async log. Error/fallback is handled in orchestration, not here.
                wiretapService.tap(message, def, ref, "error", "Kafka send failed: " + exception.getMessage());
            } else {
                wiretapService.tap(message, def, ref, "completed", "Message sent to Kafka: " + metadata.topic() + "-" + metadata.partition() + "@" + metadata.offset());
            }
        });
    }

    private byte[] convertToBytes(Object payload) {
        if (payload == null) return new byte[0];
        if (payload instanceof byte[] bytes) return bytes;
        if (payload instanceof String str) return str.getBytes(StandardCharsets.UTF_8);
        return String.valueOf(payload).getBytes(StandardCharsets.UTF_8);
    }

    private KafkaProducer<byte[], byte[]> getOrCreateProducer(DynamicKafkaHandlerConfig config, HandlerConfigRef ref) {
        String key = ref.getId();
        return producerMap.computeIfAbsent(key, k -> createProducer(config));
    }

    private KafkaProducer<byte[], byte[]> createProducer(DynamicKafkaHandlerConfig config) {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, config.getAcks() != null ? config.getAcks().toString() : "all");
        if (config.getBatchSize() != null) props.put(ProducerConfig.BATCH_SIZE_CONFIG, config.getBatchSize());
        if (config.getLingerMs() != null) props.put(ProducerConfig.LINGER_MS_CONFIG, config.getLingerMs());
        if (config.getBufferMemory() != null) props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, config.getBufferMemory());
        if (config.getRetries() != null) props.put(ProducerConfig.RETRIES_CONFIG, config.getRetries());
        if (config.getMaxInFlightRequestsPerConnection() != null)
            props.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, config.getMaxInFlightRequestsPerConnection());
        if (config.getDeliveryTimeoutMs() != null) props.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, config.getDeliveryTimeoutMs());
        if (config.getRequestTimeoutMs() != null) props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, config.getRequestTimeoutMs());
        if (config.getEnableIdempotence() != null) props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, config.getEnableIdempotence());
        if (config.getCompressionType() != null) props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, config.getCompressionType());

        // Security configs
        if (config.getSecurityProtocol() != null) props.put("security.protocol", config.getSecurityProtocol());
        if (config.getSaslMechanism() != null) props.put("sasl.mechanism", config.getSaslMechanism());
        if (config.getSaslJaasConfig() != null) props.put("sasl.jaas.config", config.getSaslJaasConfig());
        if (config.getSslTruststoreLocation() != null) props.put("ssl.truststore.location", config.getSslTruststoreLocation());
        if (config.getSslTruststorePassword() != null) props.put("ssl.truststore.password", config.getSslTruststorePassword());
        if (config.getSslKeystoreLocation() != null) props.put("ssl.keystore.location", config.getSslKeystoreLocation());
        if (config.getSslKeystorePassword() != null) props.put("ssl.keystore.password", config.getSslKeystorePassword());
        if (config.getSslKeyPassword() != null) props.put("ssl.key.password", config.getSslKeyPassword());

        if (config.getParameters() != null) {
            config.getParameters().forEach(props::putIfAbsent);
        }
        return new KafkaProducer<>(props);
    }

    // --- LIFECYCLE methods ---
    @Override
    public void pause(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getId(), k -> new AtomicBoolean()).set(true);
    }

    @Override
    public void resume(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getId(), k -> new AtomicBoolean()).set(false);
    }

    @Override
    public boolean isPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        return handlerPauseState.getOrDefault(ref.getId(), new AtomicBoolean(false)).get();
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        KafkaProducer<byte[], byte[]> producer = producerMap.remove(ref.getId());
        if (producer != null) {
            producer.close();
        }
        super.shutdown(def, ref);
    }

    @Override
    public void dispose() {
        producerMap.values().forEach(KafkaProducer::close);
        producerMap.clear();
        handlerPauseState.clear();
        super.dispose();
    }
}