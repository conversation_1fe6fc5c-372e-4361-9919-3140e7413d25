server:
  port: 8080


spring:
  datasource:
    url: **********************************************
    username: hip_user
    password: hip_password
    driver-class-name: oracle.jdbc.OracleDriver
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.Oracle12cDialect
  data:
    redis:
      host: redis-main
      port: 6379
  cloud:
    # Config server URI (used by PropertySheetFetcher)
    config:
      uri: http://config-server:8888

service:
  manager:
    name: OrderIntegrationManager
  concurrency:
    corePoolSize: 12
    maxPoolSize: 48
    queueCapacity: 1000

hip:
  kafka:
    bootstrap-servers: "kafka1:9092,kafka2:9092"
    security-protocol: SASL_PLAINTEXT
    sasl-mechanism: SCRAM-SHA-256
  wiretap:
    threadpool:
      core-size: 4
      max-size: 8
      queue-capacity: 1000
      keep-alive-seconds: 30

# Logging and monitoring configs...
logging:
  level:
    com.example.integration: INFO

