package com.dell.it.hip.config.FlowSteps;

import com.dell.it.hip.config.ConfigRef;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

public class FlowStepConfigRef implements ConfigRef, Serializable {
    private String propertyRef;  // Unique reference key (e.g., from config map)
    private String type;         // Step type (transform, router, validate, etc.)
    private String role;         // Optional: "primary", "fallback", etc. (if used)
    private String id;           // Unique step ID
    private Map<String, Object> parameters;

    public FlowStepConfigRef() {}

    public FlowStepConfigRef(String propertyRef, String type, String role, String id, Map<String, Object> parameters) {
        this.propertyRef = propertyRef;
        this.type = type;
        this.role = role;
        this.id = id;
        this.parameters = parameters;
    }

    // --- Static factory for conversion from FlowStepConfig ---
    public static FlowStepConfigRef from(FlowStepConfig config) {
        if (config == null) return null;
        return new FlowStepConfigRef(
                config.getPropertyRef(),
                config.getType(),
                config.getRole(),    // If FlowStepConfig has getRole(); else pass null
                config.getId(),
                config.getParameters()
        );
    }

    // --- Getters and setters ---

    public String getPropertyRef() {
        return propertyRef;
    }
    public void setPropertyRef(String propertyRef) {
        this.propertyRef = propertyRef;
    }

    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }

    public String getRole() {
        return role;
    }
    public void setRole(String role) {
        this.role = role;
    }

    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    // --- equals/hashCode for collections, logging, etc. ---
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof FlowStepConfigRef)) return false;
        FlowStepConfigRef that = (FlowStepConfigRef) o;
        return Objects.equals(propertyRef, that.propertyRef)
                && Objects.equals(type, that.type)
                && Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(propertyRef, type, id);
    }

    @Override
    public String toString() {
        return "FlowStepConfigRef{" +
                "propertyRef='" + propertyRef + '\'' +
                ", type='" + type + '\'' +
                ", role='" + role + '\'' +
                ", id='" + id + '\'' +
                '}';
    }
}