package com.dell.it.hip.config.adapters;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
@Getter
@Setter
public class DynamicIBMMQAdapterConfig extends AdapterConfig {
    private String queueManager;
    private String queueName;
    private String channel;
    private String connName;
    private String authenticationType;
    private String username;
    private String password;

    // SSL/TLS
    private String sslCipherSuite;
    private String sslPeerName;
    private String sslKeystore;
    private String sslKeystorePassword;
    private String sslTruststore;
    private String sslTruststorePassword;

    // Charset/encoding
    private Integer ccsid;      // MQ CCSID
    private Integer encoding;   // MQ encoding
    private boolean compressed = false;

    // Performance/pooling/concurrency
    private Integer connectionPoolSize;
    private Integer concurrency;
    private Integer sessionCacheSize;
    private Long receiveTimeout;
    private Long recoveryInterval;
    private Boolean transacted;

    // Consumer
    private Boolean autoAck;
    private String messageSelector;
    private List<String> headersToExtract;

    // Advanced
    private Map<String, String> properties;
    private String deadLetterQueueName;
    private Integer maxRedeliveryAttempts;
    private String messageConverterClass;

    // Getters and setters for all above...

    // ... (omitted for brevity)
}