package com.dell.it.hip.util.validation;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;

import java.io.File;
import java.io.FileReader;
import java.io.Reader;
import java.io.StringReader;
import java.util.List;

public class CsvSchemaValidator {

	/**
     * Check basic CSV structure: non-empty header row and consistent column count.
     */
    public static boolean hasValidStructure(String csv) {
    	CSVFormat format = CSVFormat.DEFAULT.builder()
            .setHeader()                  // auto-parse first record as header
            .setSkipHeaderRecord(true)    // skip header from data records
            .setIgnoreEmptyLines(false)   // do not ignore empty lines
            .setIgnoreSurroundingSpaces(true)
            .build();

        try (Reader in = new StringReader(csv);
             CSVParser parser = new CSVParser(in, format)) {

            List<String> headers = parser.getHeaderNames();
            System.out.println("Headers: " + headers); // Debug statement
            if (headers.isEmpty()) {
                return false;
            }
            int expectedColumns = headers.size();
            for (CSVRecord record : parser) {
                System.out.println("Record: " + record); // Debug statement
                if (record.size() != expectedColumns) {
                    return false;
                }
                for (String value : record) {
                    if (value == null || value.trim().isEmpty()) {
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace(); // Print stack trace for debugging
            return false;
        }}

    /**
     * Validate CSV against a header schema file (first record contains expected headers).
     */
    public static boolean validate(String csv, String schemaPath) {
        CSVFormat schemaFormat = CSVFormat.DEFAULT.builder()
            .setHeader()                  // auto-parse header
            .setSkipHeaderRecord(true)    // skip header from data
            .setIgnoreEmptyLines(true)
            .setIgnoreSurroundingSpaces(true)
            .build();
 
        try {
            // Load expected headers
            File schemaFile = new File(schemaPath);
            List<String> expectedHeaders;
            try (Reader schemaReader = new FileReader(schemaFile);
                 CSVParser schemaParser = new CSVParser(schemaReader, schemaFormat)) {
                expectedHeaders = schemaParser.getHeaderNames();
            }
 
            // Parse incoming CSV using same format
            CSVFormat dataFormat = CSVFormat.DEFAULT.builder()
                .setHeader()                  // parse header
                .setSkipHeaderRecord(true)    // skip header
                .setIgnoreEmptyLines(true)
                .setIgnoreSurroundingSpaces(true)
                .build();
 
            try (Reader in = new StringReader(csv);
                 CSVParser parser = new CSVParser(in, dataFormat)) {
 
                List<String> actualHeaders = parser.getHeaderNames();
                if (!expectedHeaders.containsAll(actualHeaders)) {
                    return false;
                }
                int expectedCount = actualHeaders.size();
                for (CSVRecord record : parser) {
                    if (record.size() != expectedCount) {
                        return false;
                    }
                }
                return true;
            }
        } catch (Exception e) {
            return false;
        }
    }
}
