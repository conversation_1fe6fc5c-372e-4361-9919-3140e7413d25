package com.dell.it.hip.util.validation;
public class MessageFormatDetector {
    public enum Format { JSON, XML, CSV, EDI_X12, EDI_EDIFACT, UNKNOWN }

    public static String detect(String payload) {
        if (payload == null) return Format.UNKNOWN.name();
        String t = payload.trim();
        if (t.startsWith("{") || t.startsWith("["))        return Format.JSON.name();
        if (t.startsWith("<"))                             return Format.XML.name();
        if (t.startsWith("ISA"))                           return Format.EDI_X12.name();
        if (t.startsWith("UNA") || t.startsWith("UNB"))    return Format.EDI_EDIFACT.name();
        if (t.contains(",") || t.contains(";"))             return Format.CSV.name();
        return Format.UNKNOWN.name();
    }
}