package com.dell.it.hip.strategy.handlers;

import com.dell.it.hip.config.Handlers.HandlerConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;

/**
 * Marker for handler strategies that can provide a fallback HandlerConfigRef.
 */
public interface FallbackCapable {
    /**
     * Return the fallback HandlerConfigRef for a given primary, or null if not supported.
     */
    HandlerConfigRef getFallbackConfigRef(HandlerConfigRef primary);
}