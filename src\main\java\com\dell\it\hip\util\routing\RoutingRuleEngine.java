package com.dell.it.hip.util.routing;

import com.dell.it.hip.config.FlowSteps.FlowStepConfig;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.core.registry.HIPIntegrationRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * RuleEngine: Evaluates routing rules stored in Redis or config and returns a RoutingDecision.
 */
@Component
public class RoutingRuleEngine {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private HIPIntegrationRegistry hipIntegrationRegistry;

    public RoutingDecision evaluate(String ruleKey, Message<?> message, FlowStepConfig stepConfig, HIPIntegrationDefinition def) {
        // Step 1: Load rule from Redis (or fallback to DB/local config if needed)
        String ruleJson = redisTemplate.opsForValue().get("hip:routing:rule:" + ruleKey);
        if (ruleJson == null) {
            // Optionally: check config server, or return no route
            return RoutingDecision.none();
        }

        try {
            RoutingRule rule = objectMapper.readValue(ruleJson, RoutingRule.class);

            // Step 2: Evaluate the rule
            if (rule.getRouteType() == RoutingDecision.RouteType.CHANNEL) {
                // Optional: Evaluate conditions, for now route if condition passes (pseudo-logic)
                if (evaluateCondition(rule.getCondition(), message, stepConfig, def)) {
                    return RoutingDecision.forChannel(rule.getChannelName());
                }
            } else if (rule.getRouteType() == RoutingDecision.RouteType.HANDLER) {
                if (evaluateCondition(rule.getCondition(), message, stepConfig, def)) {
                    HandlerConfigRef handlerRef = hipIntegrationRegistry.findHandlerConfigRefById(rule.getHandlerConfigRefId());
                    if (handlerRef != null) {
                        return RoutingDecision.forHandler(handlerRef);
                    }
                }
            }
        } catch (Exception ex) {
            // Log and default to none
        }
        return RoutingDecision.none();
    }

    // You can implement complex condition logic here
    private boolean evaluateCondition(String condition, Message<?> message, FlowStepConfig stepConfig, HIPIntegrationDefinition def) {
        // TODO: Evaluate the rule's condition based on message/step/def.
        // For now, always true.
        return true;
    }
}