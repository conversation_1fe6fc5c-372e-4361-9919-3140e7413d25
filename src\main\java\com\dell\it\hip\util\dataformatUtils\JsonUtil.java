package com.dell.it.hip.util.dataformatUtils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class JsonUtil {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // Extract the root field name of a JSON object
    public static String getRootElement(String json) {
        try {
            JsonNode root = OBJECT_MAPPER.readTree(json);
            if (root.isObject() && root.fieldNames().hasNext()) {
                return root.fieldNames().next();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    // Extract field by JSON path (simple dot notation, e.g. "foo.bar.baz")
    public static String extractField(String json, String jsonPath) {
        try {
            JsonNode node = OBJECT_MAPPER.readTree(json);
            String[] parts = jsonPath.split("\\.");
            for (String part : parts) {
                if (node == null) return null;
                node = node.get(part);
            }
            return node != null && !node.isNull() ? node.asText() : null;
        } catch (Exception e) {
            return null;
        }
    }
}