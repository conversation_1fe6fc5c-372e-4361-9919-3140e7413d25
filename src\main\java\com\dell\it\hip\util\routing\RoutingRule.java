package com.dell.it.hip.util.routing;

import org.springframework.messaging.Message;

import java.util.Map;

public class RoutingRule {

    private RoutingDecision.RouteType routeType;
    private String channelName;            // If routing to channel
    private String handlerConfigRefId;     // If routing to handler
    private String condition;              // Optional, e.g., SpEL or custom logic string

    // Getters and setters

    public RoutingDecision.RouteType getRouteType() {
        return routeType;
    }

    public void setRouteType(RoutingDecision.RouteType routeType) {
        this.routeType = routeType;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getHandlerConfigRefId() {
        return handlerConfigRefId;
    }

    public void setHandlerConfigRefId(String handlerConfigRefId) {
        this.handlerConfigRefId = handlerConfigRefId;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }
}