package com.dell.it.hip.strategy.handlers;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.DynamicIbmmqHandlerConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.util.ArchiveService;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.RetryTemplateFactory;
import com.dell.it.hip.util.logging.WiretapService;
import com.ibm.mq.MQException;
import com.ibm.mq.MQMessage;
import com.ibm.mq.MQPutMessageOptions;
import com.ibm.mq.constants.CMQC;
import com.ibm.mq.MQQueue;
import com.ibm.mq.MQQueueManager;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Hashtable;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;


@Component("dynamicIbmmqOutputHandler")
public class DynamicIbmmqOutputHandler extends AbstractOutputHandlerStrategy {

    private final Map<String, MQQueueManager> qmgrMap = new ConcurrentHashMap<>();
    private final Map<String, MQQueue> queueMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicBoolean> handlerPauseState = new ConcurrentHashMap<>();
    private final Set<String> headersToFilter;

    public DynamicIbmmqOutputHandler(
            OpenTelemetryPropagationUtil otelUtil,
            WiretapService wiretapService,
            ArchiveService archiveService,
            RetryTemplateFactory retryTemplateFactory,
            Set<String> headersToFilter
    ) {
        super(otelUtil, wiretapService, archiveService, retryTemplateFactory);
        this.headersToFilter = headersToFilter;
    }

    @Override
    public String getType() {
        return "ibmmq";
    }

    @Override
    protected void doHandle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        if (isPaused(def, ref)) {
            throw new IllegalStateException("Handler [" + ref.getType() + "] is paused. Message not delivered.");
        }

        DynamicIbmmqHandlerConfig config = def.getConfig(ref.getId(), DynamicIbmmqHandlerConfig.class);
        if (config == null)
            throw new IllegalArgumentException("DynamicIbmmqHandlerConfig not found for ref: " + ref.getId());

        MQQueueManager qmgr = getOrCreateQueueManager(config, ref);
        MQQueue queue = getOrCreateQueue(config, ref, qmgr);

        boolean useGzip = Boolean.TRUE.equals(config.getGzipEnabled());
        byte[] payload = useGzip ? CompressionUtil.compress(message.getPayload()) : convertToBytes(message.getPayload());

        MQMessage mqMessage = new MQMessage();

        // Set encoding and character set only if provided in config
        if (config.getEncoding() != null) {
            mqMessage.encoding = config.getEncoding();
        }
        if (config.getCcsid() != null) {
            mqMessage.characterSet = config.getCcsid();
        }

        mqMessage.format = CMQC.MQFMT_STRING;
        if (Boolean.TRUE.equals(config.getPersistent())) {
            mqMessage.persistence = CMQC.MQPER_PERSISTENT;
        } else {
            mqMessage.persistence = CMQC.MQPER_NOT_PERSISTENT;
        }

        mqMessage.write(payload);

        MQPutMessageOptions pmo = new MQPutMessageOptions(); // default options

        try {
            queue.put(mqMessage, pmo);
            wiretapService.tap(message, def, ref, "completed", "Message sent to IBM MQ: queue=" + config.getQueue());
        } catch (MQException ex) {
            wiretapService.tap(message, def, ref, "error", "IBM MQ send failed: " + ex.getMessage());
            throw ex;
        }
    }

    private byte[] convertToBytes(Object payload) {
        if (payload == null) return new byte[0];
        if (payload instanceof byte[] bytes) return bytes;
        if (payload instanceof String str) return str.getBytes(StandardCharsets.UTF_8);
        return String.valueOf(payload).getBytes(StandardCharsets.UTF_8);
    }

    private MQQueueManager getOrCreateQueueManager(DynamicIbmmqHandlerConfig config, HandlerConfigRef ref) throws MQException {
        String key = ref.getId();
        MQQueueManager qmgr = qmgrMap.get(key);
        if (qmgr == null || !qmgr.isConnected()) {
            Hashtable<String, Object> props = new Hashtable<>();
            props.put(CMQC.CHANNEL_PROPERTY, config.getChannel());
            props.put(CMQC.HOST_NAME_PROPERTY, config.getHost());
            props.put(CMQC.PORT_PROPERTY, config.getPort());
            props.put(CMQC.USER_ID_PROPERTY, config.getUsername());
            props.put(CMQC.PASSWORD_PROPERTY, config.getPassword());
            props.put(CMQC.TRANSPORT_PROPERTY, CMQC.TRANSPORT_MQSERIES_CLIENT);
            if (config.getCcsid() != null) {
                props.put(CMQC.CCSID_PROPERTY, config.getCcsid());
            }
            qmgr = new MQQueueManager(config.getQueueManager(), props);
            qmgrMap.put(key, qmgr);
        }
        return qmgr;
    }

    private MQQueue getOrCreateQueue(DynamicIbmmqHandlerConfig config, HandlerConfigRef ref, MQQueueManager qmgr) throws MQException {
        String key = ref.getId();
        MQQueue queue = queueMap.get(key);
        if (queue == null || !queue.isOpen()) {
            int openOptions = CMQC.MQOO_OUTPUT;
            queue = qmgr.accessQueue(config.getQueue(), openOptions);
            queueMap.put(key, queue);
        }
        return queue;
    }

    // --- LIFECYCLE methods ---
    @Override
    public void pause(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getId(), k -> new AtomicBoolean()).set(true);
    }

    @Override
    public void resume(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        handlerPauseState.computeIfAbsent(ref.getId(), k -> new AtomicBoolean()).set(false);
    }

    @Override
    public boolean isPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        return handlerPauseState.getOrDefault(ref.getId(), new AtomicBoolean(false)).get();
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String key = ref.getId();
        MQQueue queue = queueMap.remove(key);
        if (queue != null) try { queue.close(); } catch (Exception ignored) {}
        MQQueueManager qmgr = qmgrMap.remove(key);
        if (qmgr != null) try { qmgr.disconnect(); } catch (Exception ignored) {}
        super.shutdown(def, ref);
    }

    @Override
    public void dispose() {
        queueMap.values().forEach(q -> { try { q.close(); } catch (Exception ignored) {} });
        queueMap.clear();
        qmgrMap.values().forEach(qmgr -> { try { qmgr.disconnect(); } catch (Exception ignored) {} });
        qmgrMap.clear();
        handlerPauseState.clear();
        super.dispose();
    }
}