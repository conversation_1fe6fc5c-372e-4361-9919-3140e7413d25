package com.dell.it.hip.config.Handlers;

import com.dell.it.hip.config.RetrySettings;

import java.util.Map;

/**
 * Outbound handler configuration (for output, fallback, or dead-letter).
 */
public class HandlerConfig {
    private String type; // e.g., "kafka", "sftp", "ibmmq"
    private String role; // "primary", "fallback"
    private String configRef; // Unique per handler instance
    private Map<String, Object> parameters; // All settings
    private RetrySettings retrySettings; // Optional: Retries, timeouts, etc.

    public String getType() { return type; }
    public void setType(String t) { this.type = t; }
    public String getRole() { return role; }
    public void setRole(String r) { this.role = r; }
    public String getConfigRef() { return configRef; }
    public void setConfigRef(String c) { this.configRef = c; }
    public Map<String, Object> getParameters() { return parameters; }
    public void setParameters(Map<String, Object> p) { this.parameters = p; }
    public RetrySettings getRetrySettings() { return retrySettings; }
    public void setRetrySettings(RetrySettings retrySettings) { this.retrySettings = retrySettings; }
}