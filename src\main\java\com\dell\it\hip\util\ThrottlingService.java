package com.dell.it.hip.util;

import com.dell.it.hip.util.ThrottleSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;
public interface ThrottlingService {
    boolean tryConsumeToken(String serviceManagerName, String integrationId, String integrationVersion, ThrottleSettings settings);
    void resetThrottle(String serviceManagerName, String integrationId, String integrationVersion);
    void updateThrottle(String serviceManagerName, String integrationId, String integrationVersion, ThrottleSettings settings);
    ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationId, String integrationVersion);
}