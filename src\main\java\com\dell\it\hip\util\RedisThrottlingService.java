package com.dell.it.hip.util;

import com.dell.it.hip.core.HIPIntegrationRuntimeService;
import com.dell.it.hip.util.LocalThrottleWindow;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.ThrottlingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Production-grade Redis-backed implementation of cluster-wide throttling service for HIP integrations.
 */
@Service
public class RedisThrottlingService implements ThrottlingService {

    @Autowired private StringRedisTemplate redisTemplate;
    @Autowired private HIPIntegrationRuntimeService runtimeService;
    @Autowired(required = false) private RedisPubSubService redisPubSubService;

    @Override
    public boolean tryConsumeToken(String serviceManagerName, String integrationId, String integrationVersion, ThrottleSettings settings) {
        // Always fetch settings from runtimeService for latest!
        if (settings == null || !settings.isEnabled()) return true;
        String key = HIPRedisKeyUtil.throttleKey(serviceManagerName, integrationId, integrationVersion);
        Long count = redisTemplate.opsForValue().increment(key);
        if (count == 1) redisTemplate.expire(key, settings.getPeriodSeconds(), TimeUnit.SECONDS);
        boolean allowed = count <= settings.getMaxMessagesPerPeriod();
        if (!allowed && redisPubSubService != null) {
            long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            if (ttl > 0 && ttl <= 1) {
                redisPubSubService.publishRefill(serviceManagerName, integrationId, integrationVersion);
            }
        }
        return allowed;
    }

    @Override
    public void resetThrottle(String serviceManagerName, String integrationId, String integrationVersion) {
        redisTemplate.delete( HIPRedisKeyUtil.throttleKey(serviceManagerName, integrationId, integrationVersion));
        if (redisPubSubService != null) {
            redisPubSubService.publishRefill(serviceManagerName, integrationId, integrationVersion);
        }
    }

    @Override
    public void updateThrottle(String serviceManagerName, String integrationId, String integrationVersion, ThrottleSettings settings) {
        runtimeService.updateThrottle(serviceManagerName, integrationVersion, settings);

        // Publish cluster-wide event
        if (redisPubSubService != null) {
            redisPubSubService.publishThrottleChanged(serviceManagerName, integrationId, integrationVersion);
        }
    }

    @Override
    public ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationId, String integrationVersion) {
        // Should always fetch from runtimeService or a cache (not shown)
        return runtimeService.getThrottleSettings(serviceManagerName, integrationVersion);
    }


}