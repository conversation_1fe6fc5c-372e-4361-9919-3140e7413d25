package com.dell.it.hip.util.adapters;

import org.springframework.messaging.MessageHeaders;

import java.util.HashMap;
import java.util.Map;

public class HeaderMapperUtil {
    public static Map<String, Object> filterHeaders(MessageHeaders headers, String... keys) {
        Map<String, Object> map = new HashMap<>();
        for (String key : keys) {
            if (headers.containsKey(key)) {
                map.put(key, headers.get(key));
            }
        }
        return map;
    }
}