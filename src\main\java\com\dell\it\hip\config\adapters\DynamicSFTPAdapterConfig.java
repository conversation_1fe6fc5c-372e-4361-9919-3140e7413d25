package com.dell.it.hip.config.adapters;

import com.dell.it.hip.config.adapters.AdapterConfig;
import com.dell.it.hip.util.ThrottleSettings;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
@Getter
@Setter
public class DynamicSFTPAdapterConfig extends AdapterConfig {
    private String host;
    private Integer port = 22;
    private String username;
    private String password;
    private String privateKey;
    private String privateKeyPassphrase;
    private String knownHostsFile;
    private String remoteDirectory;
    private String fileNamePattern;
    private Long pollingIntervalMs = 60000L;
    private boolean compressed = false;
    private String charset = "UTF-8";
    private List<String> headersToExtract;
    private String postProcessAction; // delete/rename
    private String renamePattern;
    private String deadLetterDirectory;
    private Integer maxFileSizeBytes;
    private Integer minFileAgeMs;
    private Integer concurrency = 1;
    private Map<String, String> properties;
    private ThrottleSettings throttleSettings;

    // === Callback additions ===
    private String callbackUrl;
    private String callbackToken; // optional for securing callback

    // Getters & setters
}