package com.dell.it.hip.strategy.adapters;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.config.adapters.DynamicRabbitMQAdapterConfig;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.logging.WiretapService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.amqp.support.converter.SimpleMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component("rabbitmq")
public class DynamicRabbitMQInputAdapter extends AbstractDynamicInputAdapter {

    private final Logger logger = LoggerFactory.getLogger(DynamicRabbitMQInputAdapter.class);

    @Autowired
    private Map<String, MessageChannel> inputChannels; // inputChannelName -> MessageChannel

    @Autowired
    private CachingConnectionFactory rabbitConnectionFactory;

    @Autowired
    private WiretapService wiretapService;

    @Autowired
    private OpenTelemetryPropagationUtil openTelemetryPropagationUtil;

    @Override
    public String getType() {
        return "rabbitmq";
    }

    @Override
    public void buildProducer(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        DynamicRabbitMQAdapterConfig cfg = (DynamicRabbitMQAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (cfg == null) {
            throw new IllegalStateException("No config found for RabbitMQ adapter ref: " + ref.getPropertyRef());
        }

        configureConnectionFactory(cfg);

        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(rabbitConnectionFactory);
        container.setQueueNames(cfg.getQueueName());
        if (cfg.getConcurrency() != null && cfg.getConcurrency() > 1) {
            container.setConcurrentConsumers(cfg.getConcurrency());
        }
        if (cfg.getPrefetchCount() != null) {
            container.setPrefetchCount(cfg.getPrefetchCount());
        }
        if (cfg.getAcknowledgeMode() != null) {
            container.setAcknowledgeMode(cfg.getAcknowledgeMode());
        }
        if (cfg.getChannelCacheSize() != null) {
            rabbitConnectionFactory.setChannelCacheSize(cfg.getChannelCacheSize());
        }

        MessageChannel inputChannel = getInputChannel(def);
        MessageConverter converter = getMessageConverter(cfg);

        container.setupMessageListener((ChannelAwareMessageListener) (message, channel) -> {
            try {
                Object value = converter.fromMessage(message);
                if (cfg.isCompressed() && value instanceof byte[] bytes) {
                    value = CompressionUtil.decompress(bytes);
                }

                // Build internal message
                org.springframework.messaging.Message<?> inboundMsg = toMessage(def, ref, message);

                // --- OpenTelemetry trace propagation ---
                openTelemetryPropagationUtil.propagate(inboundMsg);

                // --- WIRETAP: Only for message received ---
                wiretapService.tap(
                        inboundMsg,
                        def,
                        ref,
                        "received",
                        "Message received from RabbitMQ, queue=" + cfg.getQueueName()
                );

                // Send to next stage in flow
                processInboundMessage(def, ref, inboundMsg, inputChannel);

                // Ack handled by container per ack mode

            } catch (Exception ex) {
                logger.error("RabbitMQ message handling error for queue={}, ref={}: {}", cfg.getQueueName(), ref.getId(), ex.getMessage(), ex);
                wiretapService.tap(null, def, ref, "error",
                        "RabbitMQ message handling error: " + ex.getMessage());
            }
        });

        RabbitAdapterInstance instance = new RabbitAdapterInstance(container, inputChannel, cfg.getQueueName(), ref);
        registerAdapterInstance(def, ref, instance);

        container.start();
        logger.info("RabbitMQ listener started for queue={}, ref={}", cfg.getQueueName(), ref.getId());
    }

    @Override
    protected org.springframework.messaging.Message<?> toMessage(HIPIntegrationDefinition def, AdapterConfigRef ref, Object raw) {
        Message rabbitMsg = (Message) raw;
        MessageProperties props = rabbitMsg.getMessageProperties();
        Object payload = rabbitMsg.getBody();

        MessageBuilder<Object> mb = MessageBuilder.withPayload(payload);

        // Extract headers as Map<String, Object>
        Map<String, Object> headerMap = new HashMap<>();
        if (props != null) {
            props.getHeaders().forEach(headerMap::put);
            if (props.getCorrelationId() != null) headerMap.put("correlationId", props.getCorrelationId());
            if (props.getMessageId() != null) headerMap.put("messageId", props.getMessageId());
            if (props.getType() != null) headerMap.put("type", props.getType());
            if (props.getAppId() != null) headerMap.put("appId", props.getAppId());
            if (props.getUserId() != null) headerMap.put("userId", props.getUserId());
        }

        promoteHeaders(headerMap, mb, def, ref);

        mb.setHeader("rabbitmq_queue", props != null ? props.getConsumerQueue() : null);
        mb.setHeader("rabbitmq_deliveryTag", props != null ? props.getDeliveryTag() : null);

        return mb.build();
    }

    @Override
    protected void shutdownAdapterInstance(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof RabbitAdapterInstance rabbitInst) {
            rabbitInst.container.stop();
            logger.info("RabbitMQ container stopped for ref={}", ref.getId());
        }
    }

    @Override
    protected void startAdapterInstance(AdapterInstance instance) {
        if (instance instanceof RabbitAdapterInstance rabbitInst) {
            rabbitInst.container.start();
            logger.info("RabbitMQ container started for ref={}", rabbitInst.ref.getId());
        }
    }

    @Override
    protected void stopAdapterInstance(AdapterInstance instance) {
        if (instance instanceof RabbitAdapterInstance rabbitInst) {
            rabbitInst.container.stop();
            logger.info("RabbitMQ container stopped for ref={}", rabbitInst.ref.getId());
        }
    }

    @Override
    protected void doPause(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof RabbitAdapterInstance rabbitInst) {
            SimpleMessageListenerContainer container = rabbitInst.container;
            if (container.isRunning()) {
                container.stop();
                logger.info("RabbitMQ consumer paused for ref={}", ref.getId());
            }
        }
    }

    @Override
    protected void doResume(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof RabbitAdapterInstance rabbitInst) {
            SimpleMessageListenerContainer container = rabbitInst.container;
            if (!container.isRunning()) {
                container.start();
                logger.info("RabbitMQ consumer resumed for ref={}", ref.getId());
            }
        }
    }

    protected void configureConnectionFactory(DynamicRabbitMQAdapterConfig cfg) {
        String authType = cfg.getAuthenticationType();
        if ("TLS".equalsIgnoreCase(authType) || "CERT".equalsIgnoreCase(authType)) {
            try {
                rabbitConnectionFactory.getRabbitConnectionFactory().useSslProtocol();
            } catch (Exception e) {
                logger.error("Failed to enable TLS on RabbitMQ connection: {}", e.getMessage(), e);
                throw new IllegalStateException("RabbitMQ SSL setup failed", e);
            }
        }
        if (cfg.getUsername() != null) rabbitConnectionFactory.setUsername(cfg.getUsername());
        if (cfg.getPassword() != null) rabbitConnectionFactory.setPassword(cfg.getPassword());
        if (cfg.getVirtualHost() != null) rabbitConnectionFactory.setVirtualHost(cfg.getVirtualHost());
        if (cfg.getHost() != null) rabbitConnectionFactory.setHost(cfg.getHost());
        if (cfg.getPort() != null) rabbitConnectionFactory.setPort(cfg.getPort());
        if (cfg.getChannelCacheSize() != null) rabbitConnectionFactory.setChannelCacheSize(cfg.getChannelCacheSize());
    }

    protected MessageConverter getMessageConverter(DynamicRabbitMQAdapterConfig cfg) {
        return new SimpleMessageConverter();
    }

    public static class RabbitAdapterInstance extends AdapterInstance {
        final SimpleMessageListenerContainer container;
        final MessageChannel inputChannel;
        final String queueName;
        final AdapterConfigRef ref;

        public RabbitAdapterInstance(
                SimpleMessageListenerContainer container,
                MessageChannel inputChannel,
                String queueName,
                AdapterConfigRef ref
        ) {
            this.container = container;
            this.inputChannel = inputChannel;
            this.queueName = queueName;
            this.ref = ref;
        }
    }
}