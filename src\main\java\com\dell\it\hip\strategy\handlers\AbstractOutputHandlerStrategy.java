package com.dell.it.hip.strategy.handlers;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.HandlerConfig;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.TransactionStatus;
import com.dell.it.hip.util.ArchiveService;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.RetryTemplateFactory;
import com.dell.it.hip.util.logging.WiretapService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.messaging.Message;
import org.springframework.retry.support.RetryTemplate;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;
import com.dell.it.hip.util.TransactionLoggingUtil;

/**
 * Abstract base for all output handler strategies (<PERSON><PERSON><PERSON>, SFTP, IBM MQ, etc.)
 * Handles pause/resume, wiretap, termination logging, archiving, retries, and consistent keying.
 */
public abstract class AbstractOutputHandlerStrategy implements HandlerStrategy {

    protected final OpenTelemetryPropagationUtil otelUtil;
    protected final WiretapService wiretapService;
    protected final ArchiveService archiveService;
    protected final RetryTemplateFactory retryTemplateFactory;

    // Map for pause/resume state, keyed per handler instance
    protected final ConcurrentMap<String, AtomicBoolean> pauseMap = new ConcurrentHashMap<>();

    public AbstractOutputHandlerStrategy(OpenTelemetryPropagationUtil otelUtil,
                                         WiretapService wiretapService,
                                         ArchiveService archiveService,
                                         RetryTemplateFactory retryTemplateFactory) {
        this.otelUtil = otelUtil;
        this.wiretapService = wiretapService;
        this.archiveService = archiveService;
        this.retryTemplateFactory = retryTemplateFactory;
    }

    @Override
    public void handle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception {
        String handlerKey = buildHandlerKey(def, ref);

        if (isPaused(def, ref)) {
            wiretapService.tap(message, def, ref, "info", "Handler is paused: " + handlerKey);
            throw new IllegalStateException("Handler is paused: " + handlerKey);
        }

        otelUtil.propagate(message);

        wiretapService.tap(message, def, ref, "info", "Message received by handler: " + handlerKey);
        HandlerConfig config = def.getConfig(ref.getId(), HandlerConfig.class);
        if (isArchiveEnabled(config)) {
            archiveService.archive(message, def, ref);
        }

        boolean success = false;
        Exception error = null;

        try {
            RetryTemplate retryTemplate = getRetryTemplate(config);
            if (retryTemplate != null) {
                retryTemplate.execute(context -> {
                    doHandle(message, def, ref);
                    return null;
                });
            } else {
                doHandle(message, def, ref);
            }
            success = true;
            wiretapService.tap(message, def, ref, "completed", "Message sent successfully by handler: " + handlerKey);
        } catch (Exception ex) {
            error = ex;
            wiretapService.tap(message, def, ref, "error", "Handler error in: " + handlerKey + " | " + ex.getMessage());
            throw ex;
        } finally {
            wiretapService.tap(message, def, ref, "terminated",
                    success ? "Handler terminated (success): " + handlerKey
                            : "Handler terminated (error): " + handlerKey);

            TransactionStatus status = success ? TransactionStatus.SUCCESS : TransactionStatus.FAILURE;
            TransactionLoggingUtil.logTermination(
                    "HANDLER",                        // eventType
                    ref != null ? ref.getType() : null, // eventName (type or specific handler name)
                    ref != null ? ref.getRole() : null, // eventRole (primary/fallback)
                    def,
                    ref,
                    status,
                    (error != null) ? error.getMessage() : null,
                    message
            );
        }
    }

    @Override
    public void pause(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String handlerKey = buildHandlerKey(def, ref);
        pauseMap.computeIfAbsent(handlerKey, k -> new AtomicBoolean(true)).set(true);
    }

    @Override
    public void resume(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String handlerKey = buildHandlerKey(def, ref);
        pauseMap.computeIfAbsent(handlerKey, k -> new AtomicBoolean(false)).set(false);
    }

    @Override
    public boolean isPaused(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String handlerKey = buildHandlerKey(def, ref);
        return pauseMap.getOrDefault(handlerKey, new AtomicBoolean(false)).get();
    }

    @Override
    public void shutdown(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        // Subclasses may override to clean up protocol-specific resources.
    }

    @Override
    public void dispose() {
        // Optionally clean up resources for all handlers.
    }

    /**
     * Utility to uniquely identify a handler instance.
     * Combines service manager name, integration name, version, handler type, and ref.
     */
    protected String buildHandlerKey(HIPIntegrationDefinition def, HandlerConfigRef ref) {
        String smName = def != null ? def.getServiceManagerName() : "unknownServiceManager";
        String integrationName = def != null ? def.getHipIntegrationName() : "unknownIntegration";
        String version = def != null ? def.getVersion() : "unknownVersion";
        String type = ref != null ? ref.getType() : "unknownType";
        String refId = ref != null ? ref.getId() : "unknownRef";
        return smName + ":" + integrationName + ":" + version + ":" + type + ":" + refId;
    }

    protected boolean isArchiveEnabled(HandlerConfig config) {
        Object val = config.getParameters() != null ? config.getParameters().get("archiveEnabled") : null;
        return val != null && Boolean.parseBoolean(val.toString());
    }

    protected RetryTemplate getRetryTemplate(HandlerConfig config) {
        if (config.getRetrySettings() != null) {
            return retryTemplateFactory.create(config.getRetrySettings());
        }
        return null;
    }

    protected Object getParameter(HandlerConfig config, String key) {
        return config.getParameters() != null ? config.getParameters().get(key) : null;
    }
    /**
     * Protocol-specific output logic must be implemented by subclasses.
     */
    protected abstract void doHandle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception;
}