package com.dell.it.hip.core;

import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.util.ThrottleSettings;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;



@Service
public class HIPIntegrationRuntimeService {

    private static final Logger logger = LoggerFactory.getLogger(HIPIntegrationRuntimeService.class);

    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;

    @Value("${service.manager.name}")
    private String serviceManagerName;

    public HIPIntegrationRuntimeService(StringRedisTemplate redisTemplate, ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
    }

    // --- Redis key helpers ---

    private String statusKey(String name, String version) {
        return String.format("hip:%s:status:%s:%s", serviceManagerName, name, version);
    }

    private String throttleKey(String name, String version) {
        return String.format("hip:%s:throttle:%s:%s", serviceManagerName, name, version);
    }

    // --- Status management ---

    public void updateHIPIntegrationStatus(String hipIntegrationName, String version, IntegrationStatus status) {
        try {
            redisTemplate.opsForValue().set(statusKey(hipIntegrationName, version), status.name());
            logger.info("[RUNTIME] Updated HIPIntegrationStatus: {}:{} = {}", hipIntegrationName, version, status);
        } catch (Exception ex) {
            logger.error("Failed to update HIPIntegration status for {}:{} - {}", hipIntegrationName, version, ex.getMessage(), ex);
        }
    }

    public IntegrationStatus getHIPIntegrationStatus(String hipIntegrationName, String version) {
        String val = redisTemplate.opsForValue().get(statusKey(hipIntegrationName, version));
        if (val == null) return IntegrationStatus.UNREGISTERED;
        try {
            return IntegrationStatus.valueOf(val);
        } catch (Exception ex) {
            logger.warn("Unknown IntegrationStatus '{}', defaulting to ERROR", val);
            return IntegrationStatus.ERROR;
        }
    }

    // --- Throttle management ---

    public void updateThrottle(String hipIntegrationName, String version, ThrottleSettings settings) {
        String key = throttleKey(hipIntegrationName, version);
        try {
            if (settings != null) {
                redisTemplate.opsForValue().set(key, objectMapper.writeValueAsString(settings));
                logger.info("[RUNTIME] Throttle updated for {}:{}: {}", hipIntegrationName, version, settings);
            } else {
                redisTemplate.delete(key);
                logger.info("[RUNTIME] Throttle removed for {}:{}", hipIntegrationName, version);
            }
        } catch (Exception e) {
            logger.error("Failed to serialize throttle settings for {}:{} - {}", hipIntegrationName, version, e.getMessage(), e);
        }
    }

    public ThrottleSettings getThrottleSettings(String hipIntegrationName, String version) {
        String val = redisTemplate.opsForValue().get(throttleKey(hipIntegrationName, version));
        if (val == null) return null;
        try {
            return objectMapper.readValue(val, ThrottleSettings.class);
        } catch (Exception e) {
            logger.error("Failed to deserialize throttle settings for {}:{} - {}", hipIntegrationName, version, e.getMessage(), e);
            return null;
        }
    }

    // --- SFTP Callback event (extend for other adapter callback tracking as needed) ---

    public void recordSftpCallback(String integrationName, String version, String adapterId) {
        // Optionally persist or log the fact that a callback was received
        // Could update DB, cache, or log only
        logger.info("[RUNTIME] SFTP callback received: {}:{}:{}", integrationName, version, adapterId);
        // You can add more audit/persist logic here as needed
    }

    // --- Utility: cleanup all runtime state for an integration (optional) ---

    public void removeAllRuntimeState(String hipIntegrationName, String version) {
        try {
            redisTemplate.delete(statusKey(hipIntegrationName, version));
            redisTemplate.delete(throttleKey(hipIntegrationName, version));
            logger.info("[RUNTIME] All runtime state removed for {}:{}", hipIntegrationName, version);
        } catch (Exception e) {
            logger.error("Failed to remove all runtime state for {}:{} - {}", hipIntegrationName, version, e.getMessage(), e);
        }
    }

    // --- Optionally extend with cluster-wide hold/resume state, metrics, etc. ---

}