package com.dell.it.hip.config.Handlers;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;
@Getter
@Setter
public class DynamicKafkaHandlerConfig {
    // Unique ID for this handler config (used as propertyRef)
    private String configRef;

    // Core Kafka settings
    private String bootstrapServers;
    private String topic;
    private String clientId;

    // Security
    private String securityProtocol;         // PLAINTEXT, SSL, SASL_PLAINTEXT, SASL_SSL
    private String saslMechanism;            // PLAIN, SCRAM-SHA-256, SCRAM-SHA-512, GSSAPI, etc.
    private String saslJaasConfig;           // e.g., "org.apache.kafka.common.security.plain.PlainLoginModule required username='...' password='...';"
    private String sslTruststoreLocation;
    private String sslTruststorePassword;
    private String sslKeystoreLocation;
    private String sslKeystorePassword;
    private String sslKeyPassword;

    // Optimization/tuning
    private Integer acks;                    // 0, 1, all
    private Integer batchSize;               // bytes, e.g., 16384
    private Integer lingerMs;                // ms, e.g., 5
    private Integer bufferMemory;            // bytes, e.g., 33554432
    private Integer retries;
    private Integer maxInFlightRequestsPerConnection;
    private Integer deliveryTimeoutMs;
    private Integer requestTimeoutMs;
    private Boolean enableIdempotence;
    private Integer compressionType;         // NONE, GZIP, SNAPPY, LZ4, ZSTD (string or int)
    private Boolean gzipEnabled;             // Explicit flag for your app logic (separate from Kafka compression.type)

    // Application-level
    private Boolean archiveEnabled;          // For pre-send archiving
    private Map<String, Object> parameters;  // Any additional/custom configs (header filters, etc.)

    // Getters and setters for all fields
    // ...
    // (Omitted for brevity; use your IDE to generate)
}