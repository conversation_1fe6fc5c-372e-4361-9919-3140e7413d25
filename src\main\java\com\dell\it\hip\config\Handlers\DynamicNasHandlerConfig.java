package com.dell.it.hip.config.Handlers;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.Map;

public class DynamicNasHandlerConfig {

    // Common fields
    private String id;                    // Unique config ref
    private String protocol;              // "smb" or "nfs"
    private String host;                  // Host/IP for SMB; not needed for NFS if local mount
    private String remoteDirectory;       // Directory/share/subpath (for SMB: path within share; for NFS: local mount path)
    private String fileSeparator = "/";   // "/" for NFS, usually "\\" for SMB
    private Integer timeout;              // ms, optional, for SMB connect
    private Boolean gzipEnabled;
    private Boolean archiveEnabled;
    private Map<String, Object> parameters;

    // SMB-specific
    private String shareName;             // Required for SMB (e.g., "SHARE" in \\host\SHARE\path\to\file)
    private String username;
    private String password;
    private String domain;                // Optional: For Active Directory/Kerberos
    private Boolean useKerberos;          // Optional: enable Kerberos auth

    // NFS-specific: generally only remoteDirectory required (must be OS-mounted path)
    // If you want advanced: add nfsExport, nfsServer, mountOptions, etc. as needed

    // --- <PERSON><PERSON> and Setters ---
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }

    public String getProtocol() {
        return protocol;
    }
    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getHost() {
        return host;
    }
    public void setHost(String host) {
        this.host = host;
    }

    public String getRemoteDirectory() {
        return remoteDirectory;
    }
    public void setRemoteDirectory(String remoteDirectory) {
        this.remoteDirectory = remoteDirectory;
    }

    public String getFileSeparator() {
        return fileSeparator;
    }
    public void setFileSeparator(String fileSeparator) {
        this.fileSeparator = fileSeparator;
    }

    public Integer getTimeout() {
        return timeout;
    }
    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Boolean getGzipEnabled() {
        return gzipEnabled;
    }
    public void setGzipEnabled(Boolean gzipEnabled) {
        this.gzipEnabled = gzipEnabled;
    }

    public Boolean getArchiveEnabled() {
        return archiveEnabled;
    }
    public void setArchiveEnabled(Boolean archiveEnabled) {
        this.archiveEnabled = archiveEnabled;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public String getShareName() {
        return shareName;
    }
    public void setShareName(String shareName) {
        this.shareName = shareName;
    }

    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }
    public void setPassword(String password) {
        this.password = password;
    }

    public String getDomain() {
        return domain;
    }
    public void setDomain(String domain) {
        this.domain = domain;
    }

    public Boolean getUseKerberos() {
        return useKerberos;
    }
    public void setUseKerberos(Boolean useKerberos) {
        this.useKerberos = useKerberos;
    }
}