package com.dell.it.hip.util.adapters;

import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.core.Message;

import java.util.HashMap;
import java.util.Map;

public class RabbitMqHeaderMapperUtil {
    public static Map<String, Object> fromAmqpMessage(Message message) {
        Map<String, Object> headers = new HashMap<>();
        MessageProperties props = message.getMessageProperties();
        if (props != null) {
            if (props.getHeaders() != null) headers.putAll(props.getHeaders());
            headers.put("rabbitmq_appId", props.getAppId());
            headers.put("rabbitmq_messageId", props.getMessageId());
            headers.put("rabbitmq_correlationId", props.getCorrelationId());
            headers.put("rabbitmq_type", props.getType());
            headers.put("rabbitmq_deliveryTag", props.getDeliveryTag());
            headers.put("rabbitmq_exchange", props.getReceivedExchange());
            headers.put("rabbitmq_routingKey", props.getReceivedRoutingKey());
            headers.put("rabbitmq_redelivered", props.isRedelivered());
            headers.put("rabbitmq_priority", props.getPriority());
            headers.put("rabbitmq_timestamp", props.getTimestamp());
            // Add more as needed
        }
        return headers;
    }
}