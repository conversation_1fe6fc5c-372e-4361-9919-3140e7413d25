package com.dell.it.hip.strategy.flows;

import com.dell.it.hip.config.FlowSteps.FlowStepConfig;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;
import com.dell.it.hip.util.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.routing.RoutingDecision;
import com.dell.it.hip.util.routing.RoutingRuleEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

@Component("dynamicRouter")
public class DynamicRoutingFlowStep extends AbstractFlowStepStrategy {

    private static final Logger logger = LoggerFactory.getLogger(DynamicRoutingFlowStep.class);

    @Autowired
    private RoutingRuleEngine ruleEngine;

    @Autowired
    private WiretapService wiretapService;

    @Autowired
    private TransactionLoggingUtil transactionLoggingUtil;

    @Autowired
    private ServiceManager serviceManager;

    @Override
    public String getType() {
        return "dynamicRouter";
    }
    @Override
    protected Message<?> doExecuteStep(Message<?> message, FlowStepConfig stepConfig, HIPIntegrationDefinition def) {

            // Extract routing key/criteria
            String ruleKey = stepConfig.getParameters() != null
                    ? (String) stepConfig.getParameters().get("ruleKey")
                    : null;
            if (ruleKey == null) {
                logger.warn("No routing ruleKey specified in FlowStepConfig parameters.");
                return null;
            }

            // Evaluate routing decision
            RoutingDecision decision = ruleEngine.evaluate(ruleKey, message, stepConfig, def);

            if (decision == null) {
                logger.warn("No routing decision for ruleKey={}", ruleKey);
                return null;
            }

            if (decision.getRouteType() == RoutingDecision.RouteType.CHANNEL) {
                String channelName = decision.getChannelName();
                MessageChannel target = serviceManager.getMessageChannel(channelName);
                if (target != null) {
                    target.send(message);
                    return null;
                } else {
                    logger.error("Target channel not found: {}", channelName);
                    return null;
                }
            }

            if (decision.getRouteType() == RoutingDecision.RouteType.HANDLER) {
                HandlerConfigRef handlerRef = decision.getHandlerConfigRef();
                if (handlerRef == null) {
                    logger.error("RoutingDecision indicated HANDLER, but HandlerConfigRef is null.");
                    return null;
                }
                HandlerStrategy strategy = serviceManager.getHandlerStrategy(handlerRef.getType());
                if (strategy != null) {
                    strategy.handle(message, def, handlerRef);
                    return null;
                } else {
                    logger.error("HandlerStrategy not found for type: {}", handlerRef.getType());
                    return null;
                }
            }

            // No match
            return null;

        } catch (Exception ex) {
            logger.error("Exception in dynamic routing step: {}", ex.getMessage(), ex);
            return null;
        }
    }



}