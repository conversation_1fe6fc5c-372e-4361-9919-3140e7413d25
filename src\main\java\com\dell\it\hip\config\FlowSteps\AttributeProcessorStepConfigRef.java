package com.dell.it.hip.config.FlowSteps;

import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;

public class AttributeProcessorStepConfigRef extends FlowStepConfigRef {
    private String propertyRef;
    private String description;

    // Add step type for registry lookup
    private final String type = "attributeProcessor";

    // These will be resolved from property sheets using propertyRef as prefix
    // e.g., attributeProcessor1.attributeName, etc.

    @Override
    public String getType() { return type; }

    @Override
    public String getPropertyRef() { return propertyRef; }

    public void setPropertyRef(String ref) { this.propertyRef = ref; }

    public String getDescription() { return description; }
    public void setDescription(String desc) { this.description = desc; }
}