package com.dell.it.hip.config.adapters;

import com.dell.it.hip.config.adapters.AdapterConfig;

import java.util.Map;

public class SftpAdapterConfig extends AdapterConfig {
    private String host;
    private int port = 22;
    private String username;
    private String password;
    private String privateKey;
    private String privateKeyPassphrase;
    private String remoteDir;
    private int pollIntervalSeconds = 60;
    private String filePattern = "*"; // support regex or extension
    private String postProcessAction = "delete"; // or "rename"
    private String renamePattern;

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getPrivateKeyPassphrase() {
        return privateKeyPassphrase;
    }

    public void setPrivateKeyPassphrase(String privateKeyPassphrase) {
        this.privateKeyPassphrase = privateKeyPassphrase;
    }

    public String getRemoteDir() {
        return remoteDir;
    }

    public void setRemoteDir(String remoteDir) {
        this.remoteDir = remoteDir;
    }

    public int getPollIntervalSeconds() {
        return pollIntervalSeconds;
    }

    public void setPollIntervalSeconds(int pollIntervalSeconds) {
        this.pollIntervalSeconds = pollIntervalSeconds;
    }

    public String getFilePattern() {
        return filePattern;
    }

    public void setFilePattern(String filePattern) {
        this.filePattern = filePattern;
    }

    public String getPostProcessAction() {
        return postProcessAction;
    }

    public void setPostProcessAction(String postProcessAction) {
        this.postProcessAction = postProcessAction;
    }

    public String getRenamePattern() {
        return renamePattern;
    }

    public void setRenamePattern(String renamePattern) {
        this.renamePattern = renamePattern;
    }

    public boolean isCompressed() {
        return compressed;
    }

    public void setCompressed(boolean compressed) {
        this.compressed = compressed;
    }

    public Map<String, String> getEnrichHeaders() {
        return enrichHeaders;
    }

    public void setEnrichHeaders(Map<String, String> enrichHeaders) {
        this.enrichHeaders = enrichHeaders;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    private boolean compressed = false;
    private Map<String, String> enrichHeaders;


}