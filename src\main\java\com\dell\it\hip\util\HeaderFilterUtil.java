package com.dell.it.hip.util;

import org.springframework.messaging.MessageHeaders;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class HeaderFilterUtil {

    /**
     * Returns a new map of headers after removing keys in headersToRemove.
     */
    public static Map<String, Object> filterHeaders(MessageHeaders original, Set<String> headersToRemove) {
        Map<String, Object> filtered = new HashMap<>(original);
        if (headersToRemove != null) {
            headersToRemove.forEach(filtered::remove);
        }
        return filtered;
    }
}