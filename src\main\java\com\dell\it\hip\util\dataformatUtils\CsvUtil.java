package com.dell.it.hip.util.dataformatUtils;

import org.apache.commons.csv.*;
import java.io.*;
import java.util.*;
public class CsvUtil {
    /**
     * Extract a field by column name or zero-based column index.
     * @param csvLine a single line of CSV data (as String)
     * @param expression column name or "col[index]" (e.g., "col[2]")
     * @param headers list of column names, or null if unknown
     */
    public static String extractField(String csvLine, String expression, List<String> headers) {
        if (csvLine == null || expression == null) return null;
        String[] tokens = csvLine.split(",", -1); // don't discard trailing empty columns
        if (expression.startsWith("col[")) {
            int idx = Integer.parseInt(expression.substring(4, expression.length() - 1));
            return (idx >= 0 && idx < tokens.length) ? tokens[idx] : null;
        } else if (headers != null && !headers.isEmpty()) {
            for (int i = 0; i < headers.size(); i++) {
                if (headers.get(i).equalsIgnoreCase(expression)) {
                    return tokens[i];
                }
            }
        }
        return null;
    }
    // Convenience: for CSV without headers, use index form: "col[0]", "col[1]", etc.
    public static String extractField(String csvLine, String expression) {
        return extractField(csvLine, expression, null);
    }
}