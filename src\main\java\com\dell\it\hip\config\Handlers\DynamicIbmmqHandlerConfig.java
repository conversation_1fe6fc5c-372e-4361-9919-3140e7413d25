package com.dell.it.hip.config.Handlers;

import java.util.Map;

public class DynamicIbmmqHandlerConfig {
    private String id;
    private String queueManager;
    private String host;
    private Integer port;
    private String channel;
    private String queue;
    private String username;
    private String password;
    private Integer ccsid;         // Character set ID (e.g. 1208 for UTF-8, 819 for ISO-8859-1)
    private Integer encoding;      // Message encoding (e.g. MQENC_NATIVE)
    private Boolean persistent;
    private Boolean gzipEnabled;
    private Boolean archiveEnabled;
    private Map<String, Object> parameters;

    // Getters and setters...
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getQueueManager() { return queueManager; }
    public void setQueueManager(String queueManager) { this.queueManager = queueManager; }

    public String getHost() { return host; }
    public void setHost(String host) { this.host = host; }

    public Integer getPort() { return port; }
    public void setPort(Integer port) { this.port = port; }

    public String getChannel() { return channel; }
    public void setChannel(String channel) { this.channel = channel; }

    public String getQueue() { return queue; }
    public void setQueue(String queue) { this.queue = queue; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public Integer getCcsid() { return ccsid; }
    public void setCcsid(Integer ccsid) { this.ccsid = ccsid; }

    public Integer getEncoding() { return encoding; }
    public void setEncoding(Integer encoding) { this.encoding = encoding; }

    public Boolean getPersistent() { return persistent; }
    public void setPersistent(Boolean persistent) { this.persistent = persistent; }

    public Boolean getGzipEnabled() { return gzipEnabled; }
    public void setGzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; }

    public Boolean getArchiveEnabled() { return archiveEnabled; }
    public void setArchiveEnabled(Boolean archiveEnabled) { this.archiveEnabled = archiveEnabled; }

    public Map<String, Object> getParameters() { return parameters; }
    public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
}