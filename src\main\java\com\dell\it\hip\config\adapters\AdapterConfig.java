package com.dell.it.hip.config.adapters;

import com.dell.it.hip.config.ConfigRef;

public class AdapterConfig {
    private String type; // e.g., "kafka", "rabbitmq", "ibmmq", "sftp"
    private String role; // "primary" or "fallback"
    private String configRef; // reference to property sheet or config key
    // Additional adapter-specific fields can be added in subclasses


    public String getType() { return type; }
    public void setType(String t) { this.type = t; }
    public String getRole() { return role; }
    public void setRole(String r) { this.role = r; }
    public String getConfigRef() { return configRef; }
    public void setConfigRef(String c) { this.configRef = c; }
}