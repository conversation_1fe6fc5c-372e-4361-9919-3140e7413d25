package com.dell.it.hip.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.util.*;

@Component
public class PropertySheetFetcher {

    @Value("${spring.cloud.config.uri}")
    private String configServerUri; // e.g., http://config-server:8888

    @Value("${spring.profiles.active:default}")
    private String defaultProfile;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * Fetches and merges property sheets for a given HIPIntegration at runtime.
     * The last sheet or map in the list will override earlier values.
     */
    public Map<String, Object> fetchAndMerge(List<String> propertySheets, Map<String, Object> directOverrides) {
        Map<String, Object> merged = new LinkedHashMap<>();
        if (propertySheets != null) {
            for (String sheet : propertySheets) {
                if (sheet != null && !sheet.isEmpty()) {
                    merged.putAll(fetchSheet(sheet));
                }
            }
        }
        // Overlay direct overrides
        if (directOverrides != null && !directOverrides.isEmpty()) {
            merged.putAll(directOverrides);
        }
        return merged;
    }

    /**
     * Fetches properties for a single property sheet from the config server.
     * Assumes each sheet is represented as a Spring Cloud Config application.
     *
     * @param sheetName the application name (property sheet)
     * @return map of all properties in that sheet
     */
    public Map<String, Object> fetchSheet(String sheetName) {
        Map<String, Object> props = new HashMap<>();
        try {
            // Construct the REST URL: <config-server>/<application>/<profile>
            String url = String.format("%s/%s/%s", configServerUri, sheetName, defaultProfile);

            String json = restTemplate.getForObject(url, String.class);

            JsonNode root = objectMapper.readTree(json);
            // propertySources is an array of sources (the first one is usually the direct sheet)
            JsonNode sources = root.get("propertySources");
            if (sources != null && sources.isArray()) {
                for (JsonNode src : sources) {
                    JsonNode sourceProps = src.get("source");
                    if (sourceProps != null) {
                        Iterator<Map.Entry<String, JsonNode>> fields = sourceProps.fields();
                        while (fields.hasNext()) {
                            Map.Entry<String, JsonNode> entry = fields.next();
                            props.put(entry.getKey(), entry.getValue().asText());
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to fetch property sheet from config server: " + sheetName, e);
        }
        return props;
    }

    // Overload for when there are no direct overrides
    public Map<String, Object> fetchAndMerge(List<String> propertySheets) {
        return fetchAndMerge(propertySheets, null);
    }
}