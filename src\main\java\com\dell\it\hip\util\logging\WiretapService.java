package com.dell.it.hip.util.logging;

import com.dell.it.hip.config.ConfigRef;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.messaging.Message;
import java.time.Instant;
import java.util.concurrent.RejectedExecutionException;

@Service
public class WiretapService {

    private static final Logger logger = LoggerFactory.getLogger(WiretapService.class);
    private final ThreadPoolTaskExecutor executor;

    // Constructor injection (recommended for Spring)
    public WiretapService(@Qualifier("hipWiretapExecutor") ThreadPoolTaskExecutor executor) {
        this.executor = executor;
    }

    public void tap(Message<?> message,
                    HIPIntegrationDefinition def,
                    ConfigRef ref,
                    String eventType,
                    String description) {
        WiretapEvent event = createEvent(message, def, ref, eventType, description);
        try {
            executor.submit(() -> doWiretap(event));
        } catch (RejectedExecutionException ex) {
            logger.warn("Wiretap event dropped due to full queue: {}", event, ex);
        }
    }

    private void doWiretap(WiretapEvent event) {
        try {
            logger.info("[WIRETAP][{}] integration={}, version={}, flowId={}, type={}, configRef={}, msgId={}, desc={}",
                    event.getEventType(),
                    event.getIntegrationName(),
                    event.getIntegrationVersion(),
                    event.getFlowId(),
                    event.getHandlerType(),
                    event.getHandlerConfigRef(),
                    event.getMessageId(),
                    event.getHipIntegrationType(),
                    event.getBusinessFlowVersion(),
                    event.getBusinessFlowType(),
                    event.getDescription()

            );
            // TODO: persist/emit to backend, if needed.
        } catch (Exception e) {
            logger.error("WiretapService error: {}", e.getMessage(), e);
        }
    }

    private WiretapEvent createEvent(Message<?> message, HIPIntegrationDefinition def, ConfigRef ref, String eventType, String description) {
        WiretapEvent event = new WiretapEvent();
        event.setTimestamp(Instant.now());
        event.setEventType(eventType);
        event.setDescription(description);
        event.setIntegrationName(def != null ? def.getHipIntegrationName() : null);
        event.setIntegrationVersion(def != null ? def.getVersion() : null);
        event.setBusinessFlowType(def != null ? def.getBusinessFlowType() : null);
        event.setHipIntegrationType(def != null ? def.getHipIntegrationType() : null);
        event.setBusinessFlowVersion(def != null ? def.getBusinessFlowVersion() : null);
        event.setBusinessFlowName(def != null ? def.getBusinessFlowName() : null);
        event.setHandlerType(ref != null ? ref.getType() : null);
        event.setHandlerConfigRef(ref != null ? ref.getId() : null);
        event.setMessageId(message != null && message.getHeaders().containsKey("id")
                ? message.getHeaders().get("id").toString()
                : null);
        event.setPayload(message != null ? message.getPayload() : null);
        // Optionally, set flowId or other fields as needed.
        return event;
    }

    // Optionally define a WiretapEvent POJO
    @lombok.Getter
    @lombok.Setter
    public static class WiretapEvent {
        private Instant timestamp;
        private String eventType;
        private String description;
        private String flowId;
        private String handlerType;      // "adapter" or "handler" type
        private String handlerConfigRef; // Adapter or Handler ref ID
        private String messageId;
        private Object payload;
        private String integrationName;
        private String integrationVersion;
        private String businessFlowName;
        private String businessFlowType;
        private String hipIntegrationType;
        private String businessFlowVersion;
    }
}