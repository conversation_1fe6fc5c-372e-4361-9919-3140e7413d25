package com.dell.it.hip.config.adapters;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;
@Getter
@Setter
public class DynamicNASAdapterConfig {

    // --- Protocol ---
    private String protocol;           // "nfs" or "smb"

    // --- SMB-specific fields ---
    private String host;               // SMB hostname/IP
    private String shareName;          // SMB share name
    private String domain;             // AD domain, optional
    private String username;
    private String password;
    private String mountPath;          // OS-mounted path, e.g. /mnt/smbshare

    // --- Common fields ---
    private String remoteDirectory;    // Directory inside share or NFS mount

    // --- Polling and file selection ---
    private String fileNamePattern;    // Regex for files, e.g. ".*\\.csv"
    private String fileSortOrder;      // "OLDEST", "NEWEST", or null
    private Integer maxFilesPerPoll;   // Limit per poll cycle
    private Long pollingIntervalMs;    // How often to poll (ms), default 60000
    private Long fileAgeMs;            // Minimum file age before pickup (ms)
    private Boolean ignoreHiddenFiles; // Skip hidden files

    // --- Content handling ---
    private String charset;            // e.g., "UTF-8"
    private Boolean compressed;        // Is file GZIP-compressed?

    // --- Post-processing ---
    private String postProcessAction;  // "delete", "rename", or "move"
    private String renamePattern;      // e.g. "{file}.processed"
    private String moveDirectory;      // Directory to move after pickup (optional)

    // --- Extensibility/future options ---
    private Map<String, Object> parameters; // Extra options as needed

    // Optionally add: SMB port, advanced timeouts, retry, etc.
}