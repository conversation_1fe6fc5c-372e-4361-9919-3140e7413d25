package com.dell.it.hip.config;

public class ClusterEventMessage {
    private ClusterEventType eventType;
    private String integrationId;
    private String serviceManagerName;
    private String integrationName;
    private String version;
    private Long timestamp;
    private Object payload; // throttle settings, hold flag, etc

    // Getters/Setters
    public ClusterEventType getEventType() { return eventType; }
    public void setEventType(ClusterEventType eventType) { this.eventType = eventType; }
    public String getIntegrationId() { return integrationId; }
    public void setIntegrationId(String integrationId) { this.integrationId = integrationId; }
    public String getServiceManagerName() { return serviceManagerName; }
    public void setServiceManagerName(String serviceManagerName) { this.serviceManagerName = serviceManagerName; }
    public String getIntegrationName() { return integrationName; }
    public void setIntegrationName(String integrationName) { this.integrationName = integrationName; }
    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }
    public Long getTimestamp() { return timestamp; }
    public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }
    public Object getPayload() { return payload; }
    public void setPayload(Object payload) { this.payload = payload; }

    public enum ClusterEventType {
        REGISTER,
        UNREGISTER,
        UPDATE,
        HOLD,
        RELEASE,
        APPLY_THROTTLE,
        REMOVE_THROTTLE,
        STRICT_ORDER_ACTIVATE,
        STRICT_ORDER_RELEASE,
        DEDUP_ACTIVATE,
        DEDUP_RELEASE,
        FLOWSTEP_ERROR
        // ... add more as needed
    }
}