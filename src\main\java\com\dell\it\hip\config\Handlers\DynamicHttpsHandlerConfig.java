package com.dell.it.hip.config.Handlers;

import com.dell.it.hip.config.Handlers.HandlerConfig;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
@Getter
@Setter
public class DynamicHttpsHandlerConfig extends HandlerConfig {
    private String endpointUrl;
    private String httpMethod; // GET, POST, PUT, etc.
    private Map<String, String> headers; // default headers
    private String apiKeyHeader; // e.g. "x-api-key"
    private String apiKeyValue;
    private String sslKeystorePath;
    private String sslKeystorePassword;
    private String sslTruststorePath;
    private String sslTruststorePassword;
    private Long connectTimeoutMs;
    private Long readTimeoutMs;
    private Integer maxInMemorySize;
    private Integer maxConnections;
    private Integer maxConcurrency;
    private Integer retryAttempts;
    private Long retryBackoffMs;
    private Boolean compressed; // gzip or not
    private List<String> headersToExtract;
    private Boolean enableWiretap;
    private Map<String, Object> properties; // any additional custom settings
    // OAuth2
    private Boolean oauthEnabled;
    private String oauthTokenUrl;
    private String oauthClientId;
    private String oauthClientSecret;
    private String oauthScope;
    private String oauthAudience;
    private Map<String, String> oauthAdditionalParams;

    public Boolean isOauthEnabled() { return oauthEnabled; }



    // Getters and setters ...
}