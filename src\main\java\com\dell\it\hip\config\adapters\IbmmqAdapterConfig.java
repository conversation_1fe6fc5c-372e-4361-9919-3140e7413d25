package com.dell.it.hip.config.adapters;

import org.springframework.context.annotation.Configuration;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
public class IbmmqAdapterConfig extends AdapterConfig {
        // Core MQ connection
        private String host;
        private int port;
        private String queueManager;
        private String channel;
        private String queue;
        private String username;
        private String password;

        // SSL/Certificate Support
        private boolean sslEnabled;
        private String cipherSuite;         // e.g., "TLS_RSA_WITH_AES_256_CBC_SHA256"
        private String sslPeerName;
        private String keyStorePath;
        private String keyStorePassword;
        private String trustStorePath;
        private String trustStorePassword;

        // Additional config
        private int concurrency = 1;
        private boolean compressed;
        private Map<String, String> enrichHeaders;

        public IbmmqAdapterConfig() {}

        // Getters and setters...

        public String getHost() { return host; }
        public void setHost(String host) { this.host = host; }

        public int getPort() { return port; }
        public void setPort(int port) { this.port = port; }

        public String getQueueManager() { return queueManager; }
        public void setQueueManager(String queueManager) { this.queueManager = queueManager; }

        public String getChannel() { return channel; }
        public void setChannel(String channel) { this.channel = channel; }

        public String getQueue() { return queue; }
        public void setQueue(String queue) { this.queue = queue; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }

        public boolean isSslEnabled() { return sslEnabled; }
        public void setSslEnabled(boolean sslEnabled) { this.sslEnabled = sslEnabled; }

        public String getCipherSuite() { return cipherSuite; }
        public void setCipherSuite(String cipherSuite) { this.cipherSuite = cipherSuite; }

        public String getSslPeerName() { return sslPeerName; }
        public void setSslPeerName(String sslPeerName) { this.sslPeerName = sslPeerName; }

        public String getKeyStorePath() { return keyStorePath; }
        public void setKeyStorePath(String keyStorePath) { this.keyStorePath = keyStorePath; }

        public String getKeyStorePassword() { return keyStorePassword; }
        public void setKeyStorePassword(String keyStorePassword) { this.keyStorePassword = keyStorePassword; }

        public String getTrustStorePath() { return trustStorePath; }
        public void setTrustStorePath(String trustStorePath) { this.trustStorePath = trustStorePath; }

        public String getTrustStorePassword() { return trustStorePassword; }
        public void setTrustStorePassword(String trustStorePassword) { this.trustStorePassword = trustStorePassword; }

        public int getConcurrency() { return concurrency; }
        public void setConcurrency(int concurrency) { this.concurrency = concurrency; }

        public boolean isCompressed() { return compressed; }
        public void setCompressed(boolean compressed) { this.compressed = compressed; }

        public Map<String, String> getEnrichHeaders() { return enrichHeaders; }
        public void setEnrichHeaders(Map<String, String> enrichHeaders) { this.enrichHeaders = enrichHeaders; }
}