package com.dell.it.hip.util.validation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.networknt.schema.JsonSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.SpecVersion;
import com.networknt.schema.ValidationMessage;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Set;
@Slf4j
public class JsonSchemaValidator {

    public static boolean isWellFormed(String json) {
        try {
            new JSONObject(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

	public static boolean validate(String json, String schemaPath) throws Exception {
		try {
			// Create ObjectMapper for parsing JSON and Schema
			ObjectMapper objectMapper = new ObjectMapper();
			
			// Ensure the schemaFilePath is valid
	        if (schemaPath == null || schemaPath.isEmpty()) {
	            throw new IllegalArgumentException("Schema file path is invalid.");
	        }
			

			// Read the schema file content
			String schemaContent = new String(Files.readAllBytes(Paths.get(schemaPath)));


			// Parse the schema string into a JsonNode
			JsonNode schemaNode = objectMapper.readTree(schemaContent);


			// Create JsonSchemaFactory with the correct meta schema URI
			JsonSchemaFactory factory = JsonSchemaFactory.builder(JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012)).build();

			// Create JsonSchema object from the schema node
			JsonSchema schema = factory.getSchema(schemaNode);


			// Parse the JSON string into a JsonNode
			JsonNode jsonNode = objectMapper.readTree(json);

			// Validate the JSON data against the schema
			Set<ValidationMessage> validationMessages = schema.validate(jsonNode);

			if (validationMessages.isEmpty()) {
				log.info("JSON is valid against the schema.");
				return true;
			} else {
				log.info("JSON is invalid. Validation errors:");
				for (ValidationMessage validationMessage : validationMessages) {
					System.out.println(validationMessage.getMessage());
				}
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("An error occurred during validation: " + e.getMessage());
			return false;
		}
	}
}