package com.dell.it.hip.util.routing;

import com.dell.it.hip.config.Handlers.HandlerConfigRef;

public class RoutingDecision {

    public enum RouteType {
        CHANNEL, HANDLER, NONE
    }

    private final RouteType routeType;
    private final String channelName;
    private final HandlerConfigRef handlerConfigRef;

    // Constructors
    private RoutingDecision(RouteType routeType, String channelName, HandlerConfigRef handlerConfigRef) {
        this.routeType = routeType;
        this.channelName = channelName;
        this.handlerConfigRef = handlerConfigRef;
    }

    // Static factory methods
    public static RoutingDecision forChannel(String channelName) {
        return new RoutingDecision(RouteType.CHANNEL, channelName, null);
    }

    public static RoutingDecision forHandler(HandlerConfigRef handlerConfigRef) {
        return new RoutingDecision(RouteType.HANDLER, null, handlerConfigRef);
    }

    public static RoutingDecision none() {
        return new RoutingDecision(RouteType.NONE, null, null);
    }

    // Getters
    public RouteType getRouteType() {
        return routeType;
    }

    public String getChannelName() {
        return channelName;
    }

    public HandlerConfigRef getHandlerConfigRef() {
        return handlerConfigRef;
    }
}