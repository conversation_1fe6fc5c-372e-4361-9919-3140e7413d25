package com.dell.it.hip.config.adapters;

import com.dell.it.hip.config.ConfigRef;

import java.io.Serializable;

public class AdapterConfigRef implements Serializable , ConfigRef {
    private String type;          // e.g., "kafka", "ibmmq", "rabbitmq", "sftp", etc.
    private String role;          // e.g., "input", "output"
    private String id;            // Unique within the HIPIntegration (for wiring/debugging)
    private String propertyRef;   // Property sheet key for this adapter's configuration

    // --- Optional: for additional attributes per adapter, use a flexible map or extend this class ---

    // Constructors
    public AdapterConfigRef() {}

    public AdapterConfigRef(String type, String role, String id, String propertyRef) {
        this.type = type;
        this.role = role;
        this.id = id;
        this.propertyRef = propertyRef;
    }

    // Getters & setters
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }

    public String getRole() { return role; }
    public void setRole(String role) { this.role = role; }

    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getPropertyRef() { return propertyRef; }
    public void setPropertyRef(String propertyRef) { this.propertyRef = propertyRef; }
}