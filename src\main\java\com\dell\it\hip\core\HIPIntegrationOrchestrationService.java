package com.dell.it.hip.core;

import com.dell.it.hip.config.FlowSteps.FlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.core.registry.HIPIntegrationRegistry;
import com.dell.it.hip.strategy.adapters.DynamicSFTPInputAdapter;
import com.dell.it.hip.strategy.adapters.InputAdapterStrategy;
import com.dell.it.hip.strategy.flows.FlowStepStrategy;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.logging.WiretapService;

import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.*;
import org.springframework.core.task.TaskExecutor;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.dell.it.hip.core.ServiceManager;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class HIPIntegrationOrchestrationService {

    private static final Logger logger = LoggerFactory.getLogger(HIPIntegrationOrchestrationService.class);

    @Autowired private HIPIntegrationMapper hipIntegrationMapper;
    @Autowired private HIPIntegrationRegistry hipIntegrationRegistry;
    @Autowired private HIPIntegrationRuntimeService hipIntegrationRuntimeService;
    @Autowired private HIPClusterCoordinationService clusterCoordinationService;
    @Autowired private TaskExecutor flowExecutor;
    @Autowired private ServiceManager serviceManager;
    @Autowired private WiretapService wiretapService;
    @Autowired(required = false) private org.springframework.retry.support.RetryTemplate retryTemplate;

    @Value("${service.manager.name}")
    private String serviceManagerName;

    // For quick lookup of topology structure (channels per integration)
    private final Map<String, FlowTopology> topologyMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        logger.info("Initializing all HIPIntegrations at application startup...");
        List<HIPIntegrationRequestEntity> entities = hipIntegrationRegistry.findByServiceManagerName(serviceManagerName);
        for (HIPIntegrationRequestEntity entity : entities) {
            try {
                HIPIntegrationRequest req = hipIntegrationMapper.toPojo(entity);
                registerInternal(req, false);
            } catch (Exception ex) {
                logger.error("Failed to re-initialize HIPIntegration from entity {}: {}", entity.getHipIntegrationName(), ex.getMessage(), ex);
            }
        }
    }

    @Transactional
    public void registerHIPIntegration(HIPIntegrationRequest request) {
        request.setServiceManagerName(serviceManagerName);
        hipIntegrationRegistry.save(hipIntegrationMapper.toEntity(request));
        registerInternal(request, true);
        logger.info("Registered new HIPIntegration: {}:{}", request.getHipIntegrationName(), request.getVersion());
    }

    private void registerInternal(HIPIntegrationRequest req, boolean broadcast) {
        HIPIntegrationDefinition def = hipIntegrationMapper.mapToDefinition(req);

        serviceManager.getInputAdapterStrategyMap().values().forEach(strat -> strat.buildProducers(def));

        List<MessageChannel> channels = new ArrayList<>();
        MessageChannel lastChannel = prepareAndWireIntegration(def, channels);

        serviceManager.getInputAdapterStrategyMap().values().forEach(InputAdapterStrategy::startAll);

        serviceManager.registerIntegration(def, channels);
        topologyMap.put(topologyKey(def), new FlowTopology(def, channels, lastChannel));

        hipIntegrationRuntimeService.updateHIPIntegrationStatus(def.getHipIntegrationName(), def.getVersion(), IntegrationStatus.RUNNING);

        if (broadcast) {
            clusterCoordinationService.broadcastRegistration(def);
        }
        logger.info("Wired and activated HIPIntegration: {}:{}", def.getHipIntegrationName(), def.getVersion());
    }

    private String topologyKey(HIPIntegrationDefinition def) {
        return def.getHipIntegrationName() + ":" + def.getVersion();
    }

    private MessageChannel prepareAndWireIntegration(HIPIntegrationDefinition def, List<MessageChannel> channels) {
        List<FlowStepConfigRef> stepRefs = def.getFlowStepConfigRefs();
        String inputChannelName = def.getHipIntegrationName() + "." + def.getVersion() + ".inputChannel";
        org.springframework.integration.channel.ExecutorChannel inputChannel = new org.springframework.integration.channel.ExecutorChannel(flowExecutor);
        inputChannel.setComponentName(inputChannelName);
        MessageChannel prev = inputChannel;
        channels.add(inputChannel);

        for (int i = 0; i < stepRefs.size(); i++) {
            FlowStepConfigRef stepRef = stepRefs.get(i);
            String stepChannelName = def.getHipIntegrationName() + "." + def.getVersion() + ".flowstepChannel." + i;
            org.springframework.integration.channel.ExecutorChannel stepChannel = new org.springframework.integration.channel.ExecutorChannel(flowExecutor);
            stepChannel.setComponentName(stepChannelName);
            FlowStepStrategy stepStrategy = serviceManager.getFlowStepStrategy(stepRef.getType());
            bindStepToChannel(prev, stepStrategy, def, stepRef, stepChannel);
            channels.add(stepChannel);
            prev = stepChannel;
        }

        HandlerConfigRef primaryHandlerRef = findHandlerByRole(def, "primary");
        HandlerConfigRef fallbackHandlerRef = findHandlerByRole(def, "fallback");

        if (primaryHandlerRef != null) {
            String handlerChannelName = def.getHipIntegrationName() + "." + def.getVersion() + ".handlerChannel.0";
            org.springframework.integration.channel.ExecutorChannel handlerChannel = new org.springframework.integration.channel.ExecutorChannel(flowExecutor);
            handlerChannel.setComponentName(handlerChannelName);
            HandlerStrategy handlerStrategy = serviceManager.getHandlerStrategy(primaryHandlerRef.getType());
            bindHandlerToChannelWithRetryAndFallback(prev, handlerStrategy, def, primaryHandlerRef, fallbackHandlerRef, handlerChannel);
            channels.add(handlerChannel);
            prev = handlerChannel;
        }
        return prev;
    }

    private void bindStepToChannel(
            MessageChannel from,
            FlowStepStrategy stepStrategy,
            HIPIntegrationDefinition def,
            FlowStepConfigRef ref,
            MessageChannel to
    ) {
        ((org.springframework.integration.channel.ExecutorChannel) from).subscribe(message -> {
            try {
                List<Message<?>> nextMessages = stepStrategy.executeStep(message, ref, def);
                if (nextMessages != null && !nextMessages.isEmpty()) {
                    for (Message<?> next : nextMessages) {
                        if (next != null) {
                            to.send(next);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("Error in flow step {}: {}", ref.getType(), e.getMessage(), e);
                // (Optionally) add error handling here if not handled in strategy
            }
        });
    }

    private void bindHandlerToChannelWithRetryAndFallback(
            MessageChannel from,
            HandlerStrategy handlerStrategy,
            HIPIntegrationDefinition def,
            HandlerConfigRef primaryRef,
            HandlerConfigRef fallbackRef,
            MessageChannel to) {

        ((org.springframework.integration.channel.ExecutorChannel) from).subscribe(message -> {
            boolean handled = false;
            Exception primaryEx = null;
            try {
                if (retryTemplate != null) {
                    retryTemplate.execute(context -> {
                        handlerStrategy.handle(message, def, primaryRef);
                        return null;
                    });
                } else {
                    handlerStrategy.handle(message, def, primaryRef);
                }
                handled = true;
                to.send(message);
            } catch (Exception e) {
                primaryEx = e;
                logger.warn("Primary handler {} failed: {}", primaryRef.getType(), e.getMessage());
            }
            if (!handled && fallbackRef != null) {
                HandlerStrategy fallbackStrategy = serviceManager.getHandlerStrategy(fallbackRef.getType());
                if (fallbackStrategy != null) {
                    try {
                        if (retryTemplate != null) {
                            retryTemplate.execute(context -> {
                                fallbackStrategy.handle(message, def, fallbackRef);
                                return null;
                            });
                        } else {
                            fallbackStrategy.handle(message, def, fallbackRef);
                        }
                        handled = true;
                        to.send(message);
                    } catch (Exception fallbackEx) {
                        logger.error("Fallback handler {} failed: {}", fallbackRef.getType(), fallbackEx.getMessage(), fallbackEx);
                        wiretapService.tap(
                                message,
                                def,
                                fallbackRef,
                                "error",
                                "All output handlers (primary and fallback) failed or paused. Message held for manual reprocessing. Last error: " +
                                        (fallbackEx.getMessage() != null ? fallbackEx.getMessage() : "Unknown error")
                        );
                    }
                } else {
                    logger.error("No fallback handler strategy found for type {}", fallbackRef.getType());
                    wiretapService.tap(
                            message,
                            def,
                            fallbackRef,
                            "error",
                            "Fallback handler not found, message held. Last error: " +
                                    (primaryEx != null && primaryEx.getMessage() != null ? primaryEx.getMessage() : "Unknown error")
                    );
                }
            } else if (!handled) {
                wiretapService.tap(
                        message,
                        def,
                        primaryRef,
                        "error",
                        "Primary output handler failed or paused, and no fallback available. Message held for manual reprocessing. Last error: " +
                                (primaryEx != null && primaryEx.getMessage() != null ? primaryEx.getMessage() : "Unknown error")
                );
            }
        });
    }

    public void processHIPIntegrationMessage(String hipIntegrationName, String version, Message<?> message) {
        String key = hipIntegrationName + ":" + version;
        FlowTopology topo = topologyMap.get(key);
        if (topo == null) {
            logger.warn("No topology for HIPIntegration: {}:{}", hipIntegrationName, version);
            return;
        }
        topo.getChannels().get(0).send(message); // Input channel is always first
    }

    public MessageChannel getInputChannel(String hipIntegrationName, String version) {
        FlowTopology topo = topologyMap.get(hipIntegrationName + ":" + version);
        return (topo != null && !topo.getChannels().isEmpty()) ? topo.getChannels().get(0) : null;
    }

    public void unregisterHIPIntegration(String hipIntegrationName, String version) {
        FlowTopology topology = topologyMap.remove(hipIntegrationName + ":" + version);
        if (topology == null) {
            logger.warn("No topology found during unregister for HIPIntegration: {}:{}", hipIntegrationName, version);
            return;
        }
        HIPIntegrationDefinition def = topology.getDefinition();
        serviceManager.getInputAdapterStrategyMap().values().forEach(strat -> strat.shutdown(def));
        serviceManager.unregisterIntegration(hipIntegrationName, version);
        hipIntegrationRegistry.deleteByServiceManagerNameAndHipIntegrationNameAndVersion(serviceManagerName, hipIntegrationName, version);
        hipIntegrationRuntimeService.updateHIPIntegrationStatus(hipIntegrationName, version, IntegrationStatus.UNREGISTERED);
        clusterCoordinationService.broadcastUnregistration(def);
        logger.info("Unregistered HIPIntegration: {}:{}", hipIntegrationName, version);
    }

    public void pauseHIPIntegration(String hipIntegrationName, String version) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        serviceManager.getInputAdapterStrategyMap().values().forEach(strat -> strat.pause(def));
        for (HandlerStrategy strat : serviceManager.getHandlerStrategyMap().values()) {
            for (HandlerConfigRef ref : def.getHandlerConfigRefs()) {
                strat.pause(def, ref);
            }
        }
        hipIntegrationRuntimeService.updateHIPIntegrationStatus(hipIntegrationName, version, IntegrationStatus.PAUSED);
        def.getAdapterConfigRefs().forEach(ref -> clusterCoordinationService.broadcastPauseChanged(def, ref, true));
        logger.info("Paused HIPIntegration: {}:{}", hipIntegrationName, version);
    }

    public void resumeHIPIntegration(String hipIntegrationName, String version) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        serviceManager.getInputAdapterStrategyMap().values().forEach(strat -> strat.resume(def));
        for (HandlerStrategy strat : serviceManager.getHandlerStrategyMap().values()) {
            for (HandlerConfigRef ref : def.getHandlerConfigRefs()) {
                strat.resume(def, ref);
            }
        }
        hipIntegrationRuntimeService.updateHIPIntegrationStatus(hipIntegrationName, version, IntegrationStatus.RUNNING);
        def.getAdapterConfigRefs().forEach(ref -> clusterCoordinationService.broadcastPauseChanged(def, ref, false));
        logger.info("Resumed HIPIntegration: {}:{}", hipIntegrationName, version);
    }

    // Adapter/Handler control
    public void pauseAdapter(String hipIntegrationName, String version, String adapterRef) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        AdapterConfigRef ref = def.getAdapterConfigRefs().stream()
                .filter(r -> r.getPropertyRef().equals(adapterRef))
                .findFirst()
                .orElse(null);
        if (ref != null) {
            InputAdapterStrategy strat = serviceManager.getInputAdapterStrategy(ref.getType());
            if (strat != null) strat.pause(def, ref);
        }
    }
    public void resumeAdapter(String hipIntegrationName, String version, String adapterRef) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        AdapterConfigRef ref = def.getAdapterConfigRefs().stream()
                .filter(r -> r.getPropertyRef().equals(adapterRef))
                .findFirst()
                .orElse(null);
        if (ref != null) {
            InputAdapterStrategy strat = serviceManager.getInputAdapterStrategy(ref.getType());
            if (strat != null) strat.resume(def, ref);
        }
    }
    public void pauseHandler(String hipIntegrationName, String version, String handlerRef) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        HandlerConfigRef ref = def.getHandlerConfigRefs().stream()
                .filter(r -> r.getPropertyRef().equals(handlerRef))
                .findFirst()
                .orElse(null);
        if (ref != null) {
            HandlerStrategy strat = serviceManager.getHandlerStrategy(ref.getType());
            if (strat != null) strat.pause(def, ref);
        }
    }
    public void resumeHandler(String hipIntegrationName, String version, String handlerRef) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        HandlerConfigRef ref = def.getHandlerConfigRefs().stream()
                .filter(r -> r.getPropertyRef().equals(handlerRef))
                .findFirst()
                .orElse(null);
        if (ref != null) {
            HandlerStrategy strat = serviceManager.getHandlerStrategy(ref.getType());
            if (strat != null) strat.resume(def, ref);
        }
    }

    public void applyThrottle(String hipIntegrationName, String version, ThrottleSettings settings) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        def.getAdapterConfigRefs().forEach(ref ->
                clusterCoordinationService.setThrottle(def, ref, settings)
        );
        hipIntegrationRuntimeService.updateThrottle(hipIntegrationName, version, settings);
        logger.info("Applied throttle for HIPIntegration: {}:{}", hipIntegrationName, version);
    }

    public void removeThrottle(String hipIntegrationName, String version) {
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(hipIntegrationName, version);
        def.getAdapterConfigRefs().forEach(ref ->
                clusterCoordinationService.removeThrottle(def, ref)
        );
        hipIntegrationRuntimeService.updateThrottle(hipIntegrationName, version, null);
        logger.info("Removed throttle for HIPIntegration: {}:{}", hipIntegrationName, version);
    }

    // ========== Cluster Event Handlers ==========

    public void onPauseChangedEvent(String payload) {
        // payload: integrationName:version:adapterId:PAUSED or ...:RESUMED
        try {
            String[] parts = payload.split(":");
            String integrationName = parts[0];
            String version = parts[1];
            String adapterId = parts[2];
            String action = parts[3];

            HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(integrationName, version);
            AdapterConfigRef ref = def.getAdapterConfigRefs().stream()
                    .filter(r -> r.getId().equals(adapterId))
                    .findFirst().orElse(null);

            if (ref == null) {
                logger.warn("onPauseChangedEvent: Could not find adapter for event: {}", payload);
                return;
            }

            InputAdapterStrategy strat = serviceManager.getInputAdapterStrategy(ref.getType());
            if ("PAUSED".equals(action)) {
                strat.pause(def, ref);
                logger.info("OrchestrationService: Paused {}:{}:{}", integrationName, version, adapterId);
            } else if ("RESUMED".equals(action)) {
                strat.resume(def, ref);
                logger.info("OrchestrationService: Resumed {}:{}:{}", integrationName, version, adapterId);
            }
        } catch (Exception ex) {
            logger.error("onPauseChangedEvent failed: {}", payload, ex);
        }
    }

    public void onThrottleUpdateEvent(String payload) {
        // payload: integrationName:version:adapterId:STATE
        // STATE can be: THROTTLED, THROTTLE_UPDATED, THROTTLE_REMOVED
        try {
            String[] parts = payload.split(":");
            String integrationName = parts[0];
            String version = parts[1];
            String adapterId = parts[2];
            String state = parts[3];

            HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(integrationName, version);
            AdapterConfigRef ref = def.getAdapterConfigRefs().stream()
                    .filter(r -> r.getId().equals(adapterId))
                    .findFirst().orElse(null);

            if (ref == null) {
                logger.warn("onThrottleUpdateEvent: Could not find adapter for event: {}", payload);
                return;
            }

            InputAdapterStrategy strat = serviceManager.getInputAdapterStrategy(ref.getType());

            switch (state) {
                case "THROTTLE_UPDATED":
                    logger.info("OrchestrationService: Throttle updated for {}:{}:{}", integrationName, version, adapterId);
                    break;
                case "THROTTLED":
                    logger.info("OrchestrationService: Throttle hit for {}:{}:{}", integrationName, version, adapterId);
                    break;
                case "THROTTLE_REMOVED":
                    logger.info("OrchestrationService: Throttle removed for {}:{}:{}", integrationName, version, adapterId);
                    break;
                default:
                    logger.info("OrchestrationService: Unhandled throttle state: {} for {}", state, adapterId);
            }
        } catch (Exception ex) {
            logger.error("onThrottleUpdateEvent failed: {}", payload, ex);
        }
    }

    public void onRegistrationEvent(String payload) {
        // payload: integrationName:version:REGISTERED
        try {
            String[] parts = payload.split(":");
            String integrationName = parts[0];
            String version = parts[1];
            String action = parts[2];

            if (!"REGISTERED".equals(action)) {
                logger.info("onRegistrationEvent: Unknown action: {}", action);
                return;
            }
            logger.info("OrchestrationService: Registration event for {}:{}", integrationName, version);
        } catch (Exception ex) {
            logger.error("onRegistrationEvent failed: {}", payload, ex);
        }
    }

    public void onUnregistrationEvent(String payload) {
        // payload: integrationName:version:UNREGISTERED
        try {
            String[] parts = payload.split(":");
            String integrationName = parts[0];
            String version = parts[1];
            String action = parts[2];

            if (!"UNREGISTERED".equals(action)) {
                logger.info("onUnregistrationEvent: Unknown action: {}", action);
                return;
            }
            logger.info("OrchestrationService: Unregistration event for {}:{}", integrationName, version);
        } catch (Exception ex) {
            logger.error("onUnregistrationEvent failed: {}", payload, ex);
        }
    }

    public void onCustomClusterEvent(String payload) {
        logger.info("OrchestrationService: Custom cluster event: {}", payload);
    }

    // ====== Topology and status ======

    public List<HIPIntegrationInfo> getAllHIPIntegrationsWithStatus() {
        return serviceManager.getAllDefinitions().stream()
                .map(def -> {
                    IntegrationStatus status = hipIntegrationRuntimeService.getHIPIntegrationStatus(
                            def.getHipIntegrationName(), def.getVersion());
                    return new HIPIntegrationInfo(def.getHipIntegrationName(), def.getVersion(), status);
                })
                .collect(Collectors.toList());
    }

    private HandlerConfigRef findHandlerByRole(HIPIntegrationDefinition def, String role) {
        if (role == null) return null;
        return def.getHandlerConfigRefs().stream()
                .filter(ref -> role.equalsIgnoreCase(ref.getRole()))
                .findFirst()
                .orElse(null);
    }

    public static class HIPIntegrationInfo {
        private final String hipIntegrationName;
        private final String version;
        private final IntegrationStatus status;

        public HIPIntegrationInfo(String hipIntegrationName, String version, IntegrationStatus status) {
            this.hipIntegrationName = hipIntegrationName;
            this.version = version;
            this.status = status;
        }
        public String getHipIntegrationName() { return hipIntegrationName; }
        public String getVersion() { return version; }
        public IntegrationStatus getStatus() { return status; }
    }

    // For topology visualization, etc.
    static class FlowTopology {
        private final HIPIntegrationDefinition definition;
        private final List<MessageChannel> channels;
        private final MessageChannel outputChannel;

        public FlowTopology(HIPIntegrationDefinition def, List<MessageChannel> channels, MessageChannel out) {
            this.definition = def;
            this.channels = channels;
            this.outputChannel = out;
        }
        public HIPIntegrationDefinition getDefinition() { return definition; }
        public List<MessageChannel> getChannels() { return channels; }
        public MessageChannel getOutputChannel() { return outputChannel; }
    }
    public void triggerSftpForcePoll(String integrationName, String version, String adapterId) {
        // 1. Lookup topology/def
        HIPIntegrationDefinition def = serviceManager.getIntegrationDefinition(integrationName, version);
        AdapterConfigRef ref = def.getAdapterConfigRefs().stream()
                .filter(r -> r.getId().equals(adapterId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Adapter not found: " + adapterId));

        InputAdapterStrategy strategy =serviceManager.getInputAdapterStrategyMap().get(ref.getType());
        if (strategy instanceof DynamicSFTPInputAdapter sftpAdapter) {
            sftpAdapter.triggerForcePoll(integrationName, version, adapterId);
        }
        hipIntegrationRuntimeService.recordSftpCallback(integrationName, version, adapterId);
    }
}

