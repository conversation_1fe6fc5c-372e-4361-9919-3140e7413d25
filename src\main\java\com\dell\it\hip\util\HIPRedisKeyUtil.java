package com.dell.it.hip.util;

public class HIPRedisKeyUtil {

    // Adapter Pause key (per adapter instance)
    public static String adapterPauseKey(String serviceManagerName, String integrationName, String version, String adapterId) {
        return prefix(serviceManagerName) + "adapter:pause:" + integrationName + ":" + version + ":" + adapterId;
    }

    // Throttle key (per adapter instance)
    public static String throttleKey(String serviceManagerName, String integrationName, String version, String adapterId) {
        return prefix(serviceManagerName) + "adapter:throttle:" + integrationName + ":" + version + ":" + adapterId;
    }

    // Throttle key (per integration - 3 parameter version)
    public static String throttleKey(String serviceManagerName, String integrationName, String version) {
        return prefix(serviceManagerName) + "integration:throttle:" + integrationName + ":" + version;
    }

    // Rate limit window key (per adapter instance)
    public static String rateLimitKey(String serviceManagerName, String integrationName, String version, String adapterId) {
        return prefix(serviceManagerName) + "adapter:ratelimit:" + integrationName + ":" + version + ":" + adapterId;
    }

    // Deduplication key (per adapter instance)
    public static String dedupKey(String serviceManagerName, String integrationName, String version, String adapterId) {
        return prefix(serviceManagerName) + "adapter:dedup:" + integrationName + ":" + version + ":" + adapterId;
    }

    // ===== Cluster Topics =====

    // Topic for pause/resume state changes
    public static String clusterPauseChangedTopic(String serviceManagerName) {
        return prefix(serviceManagerName) + "cluster:event:pausechanged";
    }

    // Topic for throttle state changes
    public static String clusterThrottleChangedTopic(String serviceManagerName) {
        return prefix(serviceManagerName) + "cluster:event:throttlechanged";
    }

    // Topic for cluster-wide registration
    public static String clusterRegistrationTopic(String serviceManagerName) {
        return prefix(serviceManagerName) + "cluster:event:registration";
    }

    // Topic for cluster-wide unregistration
    public static String clusterUnregistrationTopic(String serviceManagerName) {
        return prefix(serviceManagerName) + "cluster:event:unregistration";
    }

    // Topic for cluster-wide custom events
    public static String clusterEventTopic(String serviceManagerName) {
        return prefix(serviceManagerName) + "cluster:event:custom";
    }

    // Topic for rate limit refill
    public static String clusterRefillTopic(String serviceManagerName) {
        return prefix(serviceManagerName) + "cluster:event:refill";
    }

    // Topic for rate limit refill (3 parameter version)
    public static String clusterRefillTopic(String serviceManagerName, String integrationId, String integrationVersion) {
        return prefix(serviceManagerName) + "cluster:event:refill:" + integrationId + ":" + integrationVersion;
    }

    // Topic for throttle changed events (3 parameter version)
    public static String throttleChangedTopic(String serviceManagerName, String integrationId, String integrationVersion) {
        return prefix(serviceManagerName) + "cluster:event:throttlechanged:" + integrationId + ":" + integrationVersion;
    }

    // Topic for cluster hold changed events (3 parameter version)
    public static String clusterHoldChangedTopic(String serviceManagerName, String integrationId, String integrationVersion) {
        return prefix(serviceManagerName) + "cluster:event:holdchanged:" + integrationId + ":" + integrationVersion;
    }

    // ==== Utility for consistent prefix ====

    public static String prefix(String serviceManagerName) {
        return (serviceManagerName == null || serviceManagerName.isEmpty())
                ? ""
                : serviceManagerName + ":";
    }
    public static String sftpFileLockKey(String integrationName, String version, String adapterId, String fileName) {
        return "hip:sftp:filelock:" + integrationName + ":" + version + ":" + adapterId + ":" + fileName;
    }
    public static String clusterSftpForcePollTopic(String integrationName, String version, String adapterId) {
        return "hip:cluster:sftp:forcepoll:" + integrationName + ":" + version + ":" + adapterId;
    }
}